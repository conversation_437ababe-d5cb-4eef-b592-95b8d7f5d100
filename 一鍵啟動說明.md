# 🚀 課程提醒小幫手 - 一鍵啟動指南

## 📋 啟動方式選擇

我為您創建了多種啟動方式，請選擇最適合您的：

### 🎯 方式一：桌面快捷方式（推薦）

1. **雙擊 `創建桌面快捷方式.vbs`**
2. **點擊「確定」創建快捷方式**
3. **之後只需雙擊桌面上的「課程提醒小幫手」圖標**

✅ **優點**：真正的一鍵啟動，最方便
❌ **缺點**：需要先執行一次創建腳本

### 🎯 方式二：批次檔案啟動

#### 一般啟動（顯示控制台）
- **雙擊 `啟動課程提醒.bat`**
- 會顯示啟動過程和狀態

#### 靜默啟動（隱藏控制台）
- **雙擊 `啟動課程提醒-靜默.bat`**
- 背景啟動，不顯示命令視窗

✅ **優點**：無需額外設定，直接使用
❌ **缺點**：每次需要在檔案總管中找到檔案

### 🎯 方式三：PowerShell 啟動

- **右鍵點擊 `啟動課程提醒.ps1`**
- **選擇「使用 PowerShell 執行」**

✅ **優點**：更詳細的狀態顯示和錯誤處理
❌ **缺點**：可能需要設定 PowerShell 執行政策

## 🔧 設定開機自動啟動

### 方法一：放入啟動資料夾
1. 按 `Win + R` 輸入 `shell:startup`
2. 將 `啟動課程提醒-靜默.bat` 複製到開啟的資料夾
3. 重新開機後會自動啟動

### 方法二：使用應用內建功能
1. 啟動應用後，右鍵點擊系統托盤圖示
2. 勾選「開機自動啟動」選項

## 🎮 應用操作指南

### 啟動後您會看到：
- **主視窗**：顯示當前課程和下一節課
- **系統托盤圖示**：右下角有小圖示

### 常用操作：
- **關閉視窗**：應用會最小化到系統托盤
- **右鍵托盤圖示**：查看選單選項
- **設定音樂**：點擊主介面的音樂設定按鈕
- **完全退出**：托盤選單 → 退出

## 📂 檔案說明

| 檔案名稱 | 用途 | 使用方式 |
|---------|------|----------|
| `啟動課程提醒.bat` | 一般啟動（顯示過程） | 雙擊執行 |
| `啟動課程提醒-靜默.bat` | 靜默啟動（隱藏控制台） | 雙擊執行 |
| `啟動課程提醒.ps1` | PowerShell啟動腳本 | 右鍵→PowerShell執行 |
| `創建桌面快捷方式.vbs` | 創建桌面快捷方式 | 雙擊執行一次 |

## 🐛 常見問題

### Q: 雙擊 .bat 檔案沒有反應？
A: 檢查是否有防毒軟體阻擋，或嘗試「以系統管理員身分執行」

### Q: PowerShell 腳本無法執行？
A: 在 PowerShell 中執行：`Set-ExecutionPolicy RemoteSigned -Scope CurrentUser`

### Q: 系統托盤看不到圖示？
A: 檢查系統托盤的隱藏圖示區域，或重新啟動應用

### Q: 應用啟動後立即關閉？
A: 查看啟動時的錯誤訊息，通常是依賴問題，執行 `npm install`

## ✨ 推薦設定

1. **首次使用**：執行 `創建桌面快捷方式.vbs` 創建快捷方式
2. **日常使用**：雙擊桌面快捷方式
3. **開機啟動**：將靜默啟動檔案放入開機資料夾

這樣您就可以享受真正的一鍵啟動體驗！🎉
