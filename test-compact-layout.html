<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>緊湊佈局測試</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="header-main">
                <h1>課程提醒小幫手</h1>
                <div id="current-time" class="time">14:25:30</div>
            </div>
        </header>

        <main>
            <!-- 課程左右併排佈局 -->
            <div class="course-layout">
                <!-- 當前課程 -->
                <div class="current-course">
                    <div id="class-view" class="card">
                        <h2>正在上課中</h2>
                        <div class="subject">數學</div>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 65%;"></div>
                            </div>
                            <div class="progress-text">65%</div>
                        </div>
                        <h3>課前準備：</h3>
                        <ul>
                            <li>準備數學課本和習作</li>
                            <li>拿出計算機和尺規</li>
                            <li>檢查昨天的作業</li>
                        </ul>

                        <div class="countdown-container">
                            <span class="countdown-label">課前準備時間：</span>
                            <span class="countdown-timer">1:30</span>
                            <button class="countdown-settings-btn" title="設定倒數時間">⚙️</button>
                        </div>
                    </div>
                </div>

                <!-- 下一節課程 -->
                <div class="next-course">
                    <div class="card">
                        <h2>下一節課</h2>
                        <div class="subject">英語</div>
                        <div class="next-class-info">
                            <p><strong>時間：</strong>15:10</p>
                            <h3>課前準備：</h3>
                            <ul>
                                <li>拿出英語課本和習作</li>
                                <li>準備英語字典</li>
                                <li>複習上週學過的單字</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 控制項 -->
            <div class="controls-section">
                <div class="header-controls">
                    <!-- 字體大小調整控制項 -->
                    <div class="font-size-controls compact">
                        <span class="control-icon" title="字體大小">🔤</span>
                        <div class="font-size-buttons">
                            <button class="font-size-btn" data-size="tiny" title="極小字體">極小</button>
                            <button class="font-size-btn" data-size="small" title="小字體">小</button>
                            <button class="font-size-btn active" data-size="medium" title="中字體">中</button>
                            <button class="font-size-btn" data-size="large" title="大字體">大</button>
                            <button class="font-size-btn" data-size="xlarge" title="超大字體">超大</button>
                        </div>
                    </div>

                    <!-- 音樂設定控制項 -->
                    <div class="music-controls compact">
                        <span class="control-icon" title="音樂設定">🎵</span>
                        <div class="music-buttons">
                            <button class="music-btn" id="music-settings-btn" title="音樂設定">設定</button>
                            <button class="music-btn" id="test-music-btn" title="測試音樂">測試</button>
                            <button class="music-btn" id="stop-music-btn" title="停止音樂" style="display: none;">停止</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <div class="test-info" style="position: fixed; top: 20px; left: 20px; background: rgba(0,0,0,0.8); color: white; padding: 15px; border-radius: 10px; font-size: 0.9em; max-width: 300px; z-index: 999;">
        <h4 style="margin: 0 0 10px 0;">🧪 兩欄佈局測試</h4>
        <ul style="margin: 0; padding-left: 20px;">
            <li>當前課程和下一節課左右併排</li>
            <li>左欄：當前課程</li>
            <li>右欄：下一節課</li>
            <li>控制項在課程下方</li>
            <li>響應式設計，小螢幕時垂直排列</li>
        </ul>
        <div style="margin-top: 10px; font-size: 0.8em; opacity: 0.8;">
            調整視窗大小測試響應式效果
        </div>
    </div>

    <script>
        // 字體大小設定
        const fontSizeSettings = {
            tiny: {
                '--header-h1-size': '2.5em',
                '--time-size': '2.0em',
                '--card-h2-size': '1.8em',
                '--subject-size': '2.8em',
                '--card-h3-size': '1.6em',
                '--card-li-size': '1.3em',
                '--next-class-p-size': '1.5em'
            },
            small: {
                '--header-h1-size': '3.5em',
                '--time-size': '3.0em',
                '--card-h2-size': '2.7em',
                '--subject-size': '4.2em',
                '--card-h3-size': '2.4em',
                '--card-li-size': '1.95em',
                '--next-class-p-size': '2.25em'
            },
            medium: {
                '--header-h1-size': '7.0em',
                '--time-size': '6.0em',
                '--card-h2-size': '5.0em',
                '--subject-size': '7.6em',
                '--card-h3-size': '4.0em',
                '--card-li-size': '3.2em',
                '--next-class-p-size': '3.6em'
            },
            large: {
                '--header-h1-size': '9.0em',
                '--time-size': '8.0em',
                '--card-h2-size': '6.4em',
                '--subject-size': '10.0em',
                '--card-h3-size': '5.0em',
                '--card-li-size': '4.0em',
                '--next-class-p-size': '4.4em'
            },
            xlarge: {
                '--header-h1-size': '11.0em',
                '--time-size': '10.0em',
                '--card-h2-size': '8.0em',
                '--subject-size': '12.0em',
                '--card-h3-size': '6.0em',
                '--card-li-size': '5.0em',
                '--next-class-p-size': '5.6em'
            }
        };
        
        const fontSizeBtns = document.querySelectorAll('.font-size-btn');
        const musicBtns = document.querySelectorAll('.music-btn');
        
        function setFontSize(size) {
            const settings = fontSizeSettings[size];
            if (settings) {
                Object.entries(settings).forEach(([property, value]) => {
                    document.documentElement.style.setProperty(property, value);
                });
                
                fontSizeBtns.forEach(btn => btn.classList.remove('active'));
                document.querySelector(`[data-size="${size}"]`).classList.add('active');
                
                console.log(`字體大小已設定為：${size}`);
            }
        }
        
        // 字體大小按鈕事件
        fontSizeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const size = btn.dataset.size;
                setFontSize(size);
            });
        });
        
        // 音樂按鈕事件（模擬）
        document.getElementById('music-settings-btn').addEventListener('click', () => {
            alert('音樂設定功能（模擬）');
        });
        
        document.getElementById('test-music-btn').addEventListener('click', () => {
            alert('測試音樂功能（模擬）');
            document.getElementById('stop-music-btn').style.display = 'inline-block';
        });
        
        document.getElementById('stop-music-btn').addEventListener('click', () => {
            alert('停止音樂功能（模擬）');
            document.getElementById('stop-music-btn').style.display = 'none';
        });
        
        // 時間更新
        function updateTime() {
            const now = new Date();
            const timeString = now.toTimeString().slice(0, 8);
            document.getElementById('current-time').textContent = timeString;
        }
        
        setInterval(updateTime, 1000);
        updateTime();
        
        console.log('緊湊佈局測試頁面已載入');
    </script>
</body>
</html>
