/* CSS 變數 - 科技感設計系統 */
:root {
    /* 字體大小設定 */
    --header-h1-size: 5.0em;
    --time-size: 4.0em;
    --card-h2-size: 3.6em;
    --subject-size: 5.6em;
    --card-h3-size: 3.2em;
    --card-li-size: 2.6em;
    --next-class-p-size: 3.0em;
    --no-class-h2-size: 4.6em;
    --no-class-p-size: 2.8em;
    --edit-button-size: 2.4em;
    --modal-h2-size: 4.2em;
    --form-label-size: 2.8em;
    --form-input-size: 2.8em;
    --schedule-item-title-size: 3.0em;
    --schedule-item-details-size: 2.5em;

    /* 科技感色彩系統 - 高對比度版本 */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-accent: #0f3460;

    --text-primary: #ffffff;
    --text-secondary: #e0e6ed;
    --text-accent: #ffffff;

    --accent-cyan: #00d4ff;
    --accent-purple: #7c77c6;
    --accent-pink: #ff77c6;
    --accent-green: #00ff88;

    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.3);

    --glow-cyan: 0 0 20px rgba(0, 212, 255, 0.3);
    --glow-purple: 0 0 20px rgba(124, 119, 198, 0.3);
    --glow-pink: 0 0 20px rgba(255, 119, 198, 0.3);
}

/* 科技感全域樣式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* =============== 可愛風主題（小學生喜好） =============== */
/* 以粉彩色、圓角、大陰影與貼紙風裝飾為主。 */
body.theme-cute {
    font-family: 'Noto Sans TC', 'Inter', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(180deg, #FFF7FB 0%, #FDF6FF 40%, #F5FBFF 100%);
    color: #444;
}

body.theme-cute::before {
    background:
        radial-gradient(120px 120px at 15% 20%, rgba(255, 182, 193, 0.35) 0%, transparent 70%),
        radial-gradient(140px 140px at 85% 25%, rgba(173, 216, 230, 0.35) 0%, transparent 70%),
        radial-gradient(160px 160px at 30% 80%, rgba(255, 228, 181, 0.35) 0%, transparent 70%),
        radial-gradient(180px 180px at 80% 70%, rgba(221, 160, 221, 0.35) 0%, transparent 70%);
    opacity: 1;
    animation: backgroundFloat 10s ease-in-out infinite alternate;
}

@keyframes backgroundFloat {
    0% { transform: translateY(0); }
    100% { transform: translateY(-10px); }
}

/* 容器與卡片：更柔和與「捏捏感」圓角 */
body.theme-cute .container {
    background: rgba(255, 255, 255, 0.75);
    border: 3px solid #FFE7F1;
    border-radius: 28px;
    box-shadow: 0 20px 40px rgba(255, 170, 200, 0.25);
}

body.theme-cute header h1 {
    background: none;
    -webkit-text-fill-color: initial;
    color: #ff6fa7;
    text-shadow: 0 4px 0 #ffd3e4, 0 6px 18px rgba(255, 111, 167, 0.25);
}

body.theme-cute .time {
    background: #fff;
    color: #ff6fa7;
    border: 2px dashed #ffd3e4;
    box-shadow: 0 6px 0 #ffd3e4;
}

/* 卡片樣式：貼紙感邊框與陰影 */
body.theme-cute .card {
    background: #ffffff;
    border: 3px solid #F7E5FF;
    border-radius: 24px;
    box-shadow: 0 10px 0 #F7E5FF, 0 14px 26px rgba(147, 112, 219, 0.15);
}

body.theme-cute .card-header h2 {
    color: #7b68ee;
    text-shadow: none;
}

body.theme-cute .live-dot {
    background-color: #ffb703;
    box-shadow: 0 0 0 0 rgba(255, 183, 3, 0.7);
}

/* 進度條：糖果色 */
body.theme-cute .progress-bar {
    background: #fff0f6;
    border: 2px solid #ffd6e7;
}
body.theme-cute .progress-fill {
    background: linear-gradient(90deg, #ff9ec7, #ffc3a0);
    box-shadow: 0 0 0 0 rgba(255, 158, 199, 0.3);
}
body.theme-cute .progress-text {
    color: #ff6fa7;
}

/* 倒數計時器：果凍膠囊 */
body.theme-cute .countdown-timer {
    background: linear-gradient(180deg, #ffe3ee, #ffd6e7);
    color: #ff3e80;
    border: 2px solid #ffc2d8;
    box-shadow: 0 6px 0 #ffc2d8;
    font-size: 1.2em;
    padding: 12px 18px;
}
body.theme-cute .countdown-label {
    font-size: 0.8em;
    font-weight: 700;
}
body.theme-cute .countdown-settings-btn {
    color: #a0a0a0;
    font-size: 1.0em;
}

/* 科目標題：繽紛漸層字 */
body.theme-cute .card-body .subject {
    background: linear-gradient(90deg, #ff7ab6, #ffc371);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
}

/* 列表項：可愛分隔線與圓點 */
body.theme-cute .card-body ul li {
    color: #555;
    border-bottom: 2px dashed #f3e8ff;
}

/* 下一節卡片：淡紫底 */
body.theme-cute .next-class-card {
    background: #fbf4ff;
    border-color: #edd7ff;
}
body.theme-cute .next-class-card .card-header h2 {
    color: #9b5de5;
}
body.theme-cute .next-class-card p {
    color: #6b6b6b;
}

/* 統計卡片：糖果漸層 */
body.theme-cute .stats-card {
    background: linear-gradient(135deg, #ff9ec7 0%, #9b5de5 100%);
}

/* 編輯按鈕：果凍膠囊 */
body.theme-cute .edit-button {
    background: linear-gradient(180deg, #ffe3ee, #ffd6e7);
    color: #ff3e80;
    border: 2px solid #ffc2d8;
    box-shadow: 0 10px 0 #ffc2d8;
}
body.theme-cute .edit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 0 #ffc2d8, 0 14px 26px rgba(255, 126, 171, 0.25);
}

/* 控制項：可愛膠囊群組 */
body.theme-cute .header-controls {
    background: #fff;
    border: 3px solid #ffeef6;
}
body.theme-cute .font-size-controls.compact .font-size-btn,
body.theme-cute .music-controls.compact .music-btn {
    background: #fff9fb;
    color: #7a7a7a;
    border: 2px solid #ffe0ec;
}
body.theme-cute .font-size-controls.compact .font-size-btn.active {
    background: linear-gradient(180deg, #ffb8d5, #ff9ec7);
    color: #fff;
    border-color: #ff9ec7;
}
body.theme-cute .music-controls.compact .music-btn {
    background: linear-gradient(180deg, #e5ddff, #d7c8ff);
    color: #5a3fbf;
    border-color: #d7c8ff;
}

/* 表單與模態：更軟的邊與陰影 */
body.theme-cute .modal-content {
    border-radius: 26px;
}
body.theme-cute .modal-header {
    background: linear-gradient(135deg, #ff9ec7 0%, #9b5de5 100%);
}
body.theme-cute .form-group input,
body.theme-cute .form-group select,
body.theme-cute .form-group textarea {
    border: 3px solid #ffe0ec;
    border-radius: 14px;
}
body.theme-cute .form-actions .button.primary {
    background: #ff6fa7;
}
body.theme-cute .form-actions .button.secondary {
    background: #b1b1b1;
}

/* 無課顯示：溫柔綠 */
body.theme-cute #no-class-view h2 {
    color: #2fbf71;
}
body.theme-cute #no-class-view p {
    color: #7a7a7a;
}

body {
    font-family: 'Inter', 'Roboto', 'Space Grotesk', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-accent) 100%);
    color: var(--text-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    position: relative;
    overflow-x: hidden;
}

/* 科技感背景動畫 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(124, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 212, 255, 0.08) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: backgroundPulse 8s ease-in-out infinite alternate;
}

@keyframes backgroundPulse {
    0% { opacity: 0.3; }
    100% { opacity: 0.6; }
}

/* 科技感玻璃容器 */
.container {
    width: 98%;
    max-width: 1600px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    box-shadow:
        0 20px 40px var(--glass-shadow),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    padding: 50px;
    position: relative;
    overflow: hidden;
}

/* 容器內部發光效果 */
.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
    animation: topGlow 3s ease-in-out infinite alternate;
}

@keyframes topGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.8; }
}

/* 科技感 Header */
header {
    text-align: center;
    border-bottom: 1px solid var(--glass-border);
    padding-bottom: 40px;
    margin-bottom: 40px;
    position: relative;
}

header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
    box-shadow: var(--glow-cyan);
}

header h1 {
    color: var(--text-accent);
    font-size: var(--header-h1-size);
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: var(--glow-cyan);
    letter-spacing: -0.02em;
}

.time {
    font-size: var(--time-size);
    font-weight: 600;
    color: var(--text-accent);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    padding: 15px 30px;
    border-radius: 16px;
    display: inline-block;
    margin-top: 20px;
    box-shadow:
        var(--glow-cyan),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    letter-spacing: 0.05em;
}

/* 字體大小控制項樣式 */
.font-size-controls {
    margin-top: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 25px;
    flex-wrap: wrap;
}

.font-size-label {
    font-size: 0.9em;
    color: #495057;
    font-weight: 700;
}

.font-size-buttons {
    display: flex;
    gap: 15px;
    background: #f8f9fa;
    border-radius: 35px;
    padding: 8px;
    border: 4px solid #dee2e6;
}

.font-size-btn {
    background: none;
    border: none;
    padding: 15px 25px;
    border-radius: 30px;
    cursor: pointer;
    font-size: 0.7em;
    font-weight: 700;
    color: #6c757d;
    transition: all 0.3s ease;
    min-width: 60px;
    text-align: center;
}

.font-size-btn:hover {
    background-color: #e9ecef;
    color: #495057;
    transform: translateY(-2px);
}

.font-size-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    transform: translateY(-2px);
}

/* 音樂控制項樣式 */
.music-controls {
    margin-top: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 25px;
    flex-wrap: wrap;
}

.music-label {
    font-size: 0.9em;
    color: #495057;
    font-weight: 700;
}

.music-buttons {
    display: flex;
    gap: 15px;
    background: #f8f9fa;
    border-radius: 35px;
    padding: 8px;
    border: 4px solid #dee2e6;
}

.music-btn {
    background: none;
    border: none;
    padding: 15px 25px;
    border-radius: 30px;
    cursor: pointer;
    font-size: 0.7em;
    font-weight: 700;
    color: #6c757d;
    transition: all 0.3s ease;
    min-width: 120px;
    text-align: center;
}

.music-btn:hover {
    background-color: #e9ecef;
    color: #495057;
    transform: translateY(-2px);
}

.music-btn:active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
    transform: translateY(-2px);
}

/* 音樂設定表單特殊樣式 */
.form-group input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    height: 8px;
    background: #dee2e6;
    border-radius: 4px;
    outline: none;
    padding: 0;
}

.form-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #667eea;
    border-radius: 50%;
    cursor: pointer;
}

.form-group input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #667eea;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 10px;
    transform: scale(1.5);
}

#volume-display {
    font-weight: 700;
    color: #667eea;
    margin-left: 10px;
}

.form-group select option {
    padding: 10px;
}

/* 科技感卡片 */
.card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple), var(--accent-pink));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        var(--glow-cyan);
    border-color: var(--accent-cyan);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 15px;
}

.card-header h2 {
    margin: 0;
    font-size: var(--card-h2-size);
    color: var(--text-accent);
    display: flex;
    align-items: center;
    font-weight: 700;
    letter-spacing: -0.01em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

/* 進度指示器樣式 */
.progress-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 150px;
}

.progress-bar {
    flex: 1;
    height: 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    overflow: hidden;
    min-width: 100px;
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: progressShimmer 2s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-cyan), var(--accent-green));
    border-radius: 8px;
    transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    width: 0%;
    box-shadow: var(--glow-cyan);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
    border-radius: 0 8px 8px 0;
}

.progress-text {
    font-size: 0.45em;
    font-weight: 600;
    color: var(--accent-cyan);
    min-width: 40px;
    text-align: right;
    text-shadow: var(--glow-cyan);
}

/* 倒數計時器樣式 */
.countdown-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.countdown-label {
    font-size: 0.8em;
    color: var(--text-accent);
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.countdown-timer {
    font-size: 1.2em;
    font-weight: 700;
    color: var(--text-accent);
    background: linear-gradient(135deg, var(--accent-pink), #e74c3c);
    padding: 12px 18px;
    border-radius: 12px;
    min-width: 70px;
    text-align: center;
    border: 2px solid var(--accent-pink);
    box-shadow: var(--glow-pink);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.countdown-settings-btn {
    background: none;
    border: none;
    font-size: 1.0em;
    cursor: pointer;
    color: #6c757d;
    transition: color 0.3s;
    padding: 5px;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.countdown-settings-btn:hover {
    color: #495057;
    background-color: #f8f9fa;
}

.live-dot {
    height: 18px; /* Increased */
    width: 18px; /* Increased */
    background-color: #28a745; /* Green */
    border-radius: 50%;
    margin-right: 15px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 12px rgba(40, 167, 69, 0); } /* Adjusted pulse */
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

.card-body .subject {
    font-size: var(--subject-size);
    font-weight: 700;
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-green));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-top: 0;
    margin-bottom: 20px;
    text-shadow: var(--glow-cyan);
    letter-spacing: -0.02em;
}

.card-body h3 {
    font-size: var(--card-h3-size);
    color: var(--text-accent);
    margin-bottom: 15px;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

.card-body ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.card-body ul li {
    font-size: var(--card-li-size);
    padding: 12px 0;
    border-bottom: 1px solid var(--glass-border);
    color: var(--text-primary);
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.next-class-card {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.next-class-card .card-header h2 {
    color: #6c757d;
}

.next-class-card p {
    font-size: var(--next-class-p-size);
    margin: 5px 0;
    color: #495057;
}

/* 統計卡片樣式 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.stats-card .card-header h2 {
    color: white;
    font-size: var(--card-h2-size);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.stat-number {
    font-size: 1.25em;
    font-weight: 700;
    margin-bottom: 5px;
    color: #fff;
}

.stat-label {
    font-size: 0.55em;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.hidden {
    display: none !important;
}

#no-class-view h2 {
    color: #28a745;
    font-size: var(--no-class-h2-size);
    margin-bottom: 10px;
}

#no-class-view p {
    font-size: var(--no-class-p-size);
    color: #6c757d;
}

footer {
    text-align: center;
    margin-top: 30px;
    color: #6c757d;
}

.button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.1em;
    transition: background-color 0.3s;
}

.button:hover {
    background-color: #0056b3;
}

/* 編輯和匯出按鈕區域 */
.edit-section {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px dashed #dfe9f3;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.edit-button {
    background: linear-gradient(135deg, var(--accent-purple), var(--accent-pink));
    color: var(--text-accent);
    border: 1px solid var(--glass-border);
    padding: 18px 36px;
    border-radius: 16px;
    cursor: pointer;
    font-size: var(--edit-button-size);
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--glow-purple);
    display: inline-flex;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.edit-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.edit-button:hover::before {
    left: 100%;
}

.edit-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        var(--glow-purple),
        0 10px 30px rgba(124, 119, 198, 0.3);
    border-color: var(--accent-purple);
}

.edit-button:active {
    transform: translateY(-1px) scale(1.02);
}

.edit-icon {
    font-size: 1.1em;
}

/* 匯出按鈕特殊樣式 */
.export-button {
    background: linear-gradient(135deg, var(--accent-green), var(--accent-cyan)) !important;
    box-shadow: var(--glow-cyan) !important;
}

.export-button:hover {
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-green)) !important;
    box-shadow: var(--glow-cyan), 0 10px 30px rgba(0, 212, 255, 0.3) !important;
}

/* 清除資料按鈕特殊樣式 */
.clear-button {
    background: linear-gradient(135deg, #ff6b6b, #ff5252) !important;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3) !important;
}

.clear-button:hover {
    background: linear-gradient(135deg, #ff5252, #ff4444) !important;
    box-shadow: 0 6px 20px rgba(255, 82, 82, 0.4), 0 10px 30px rgba(255, 68, 68, 0.3) !important;
}

/* 匯出選項樣式 */
.export-options h3 {
    color: #495057;
    font-size: 1.4em;
    font-weight: 700;
    margin-bottom: 20px;
    margin-top: 0;
}

.export-format-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.export-format-btn {
    background: #f8f9fa;
    border: 3px solid #dee2e6;
    border-radius: 16px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
}

.export-format-btn:hover {
    background: #ffffff;
    border-color: #667eea;
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

.export-format-btn:active {
    transform: translateY(-2px) scale(1.01);
}

.export-format-btn .format-icon {
    font-size: 2.5em;
    margin-bottom: 8px;
}

.export-format-btn .format-name {
    font-size: 1.3em;
    font-weight: 700;
    color: #495057;
    margin-bottom: 4px;
}

.export-format-btn .format-desc {
    font-size: 1.0em;
    color: #6c757d;
    font-weight: 500;
}

.export-format-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.export-format-btn:hover::before {
    left: 100%;
}

.export-settings {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 2px solid #dee2e6;
}

.export-settings h3 {
    color: #495057;
    font-size: 1.4em;
    font-weight: 700;
    margin-bottom: 20px;
    margin-top: 0;
}

.export-settings .form-group {
    margin-bottom: 20px;
}

.export-settings .form-group label {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.1em;
    font-weight: 600;
    color: #495057;
    cursor: pointer;
    padding: 8px 0;
    transition: color 0.3s ease;
}

.export-settings .form-group label:hover {
    color: #667eea;
}

.export-settings .form-group input[type="checkbox"] {
    transform: scale(1.4);
    accent-color: #667eea;
}

.export-settings .form-group input[type="text"] {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    font-size: 1.1em;
    font-weight: 500;
    transition: border-color 0.3s ease;
    margin-top: 8px;
}

.export-settings .form-group input[type="text"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 可愛風格下的匯出功能樣式 */
body.theme-cute .export-button {
    background: linear-gradient(180deg, #cfeaff, #b8e6ff) !important;
    color: #2196f3 !important;
    border: 2px solid #b8e6ff !important;
    box-shadow: 0 6px 0 #b8e6ff !important;
}

body.theme-cute .export-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 0 #b8e6ff, 0 10px 20px rgba(33, 150, 243, 0.25) !important;
}

/* 可愛風格下的清除按鈕樣式 */
body.theme-cute .clear-button {
    background: linear-gradient(180deg, #ffcdd2, #ffb3ba) !important;
    color: #d32f2f !important;
    border: 2px solid #ffb3ba !important;
    box-shadow: 0 6px 0 #ffb3ba !important;
}

body.theme-cute .clear-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 0 #ffb3ba, 0 10px 20px rgba(211, 47, 47, 0.25) !important;
}

body.theme-cute .export-format-btn {
    background: #ffffff;
    border: 3px solid #e3f2fd;
    border-radius: 20px;
}

body.theme-cute .export-format-btn:hover {
    background: #f3e5f5;
    border-color: #ce93d8;
    box-shadow: 0 6px 20px rgba(206, 147, 216, 0.3);
}

body.theme-cute .export-format-btn .format-name {
    color: #7b1fa2;
}

body.theme-cute .export-format-btn .format-desc {
    color: #9c27b0;
}

body.theme-cute .export-settings h3 {
    color: #7b1fa2;
}

body.theme-cute .export-settings .form-group label {
    color: #673ab7;
}

body.theme-cute .export-settings .form-group label:hover {
    color: #9c27b0;
}

body.theme-cute .export-settings .form-group input[type="checkbox"] {
    accent-color: #e91e63;
}

body.theme-cute .export-settings .form-group input[type="text"] {
    border: 3px solid #f8bbd9;
    border-radius: 14px;
}

body.theme-cute .export-settings .form-group input[type="text"]:focus {
    border-color: #e91e63;
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
}

/* 匯入功能樣式 */
.import-section {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 2px solid #dee2e6;
}

.import-section h3 {
    color: #495057;
    font-size: 1.4em;
    font-weight: 700;
    margin-bottom: 20px;
    margin-top: 0;
}

.import-controls {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    flex-wrap: wrap;
}

.file-input-wrapper {
    flex: 0 0 auto;
}

.import-btn {
    background: #f8f9fa;
    border: 3px solid #28a745;
    border-radius: 16px;
    padding: 24px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
    min-width: 200px;
    background-color: #ffffff;
}

.import-btn:hover {
    background: #f8fff9;
    border-color: #20c997;
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.2);
}

.import-btn:active {
    transform: translateY(-2px) scale(1.01);
}

.import-btn .format-icon {
    font-size: 2.5em;
    margin-bottom: 8px;
    color: #28a745;
}

.import-btn .format-name {
    font-size: 1.3em;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 4px;
}

.import-btn .format-desc {
    font-size: 1.0em;
    color: #6c757d;
    font-weight: 500;
}

.import-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(40, 167, 69, 0.1), transparent);
    transition: left 0.5s ease;
}

.import-btn:hover::before {
    left: 100%;
}

.import-info {
    flex: 1;
    min-width: 250px;
    background: #fff3cd;
    border: 2px solid #ffeaa7;
    border-radius: 12px;
    padding: 20px;
}

.import-info p {
    margin: 0 0 10px 0;
    font-weight: 700;
    color: #856404;
}

.import-info ul {
    margin: 0;
    padding-left: 20px;
    color: #856404;
}

.import-info li {
    margin-bottom: 5px;
    font-size: 0.95em;
    line-height: 1.4;
}

.reset-schedule-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 600;
    margin-top: 15px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.reset-schedule-btn:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.export-js-btn {
    background: #17a2b8;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: 600;
    margin-top: 10px;
    margin-left: 10px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.export-js-btn:hover {
    background: #138496;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

/* 可愛風格下的匯入功能樣式 */
body.theme-cute .import-section h3 {
    color: #7b1fa2;
}

body.theme-cute .import-btn {
    background: #ffffff;
    border: 3px solid #4caf50;
    border-radius: 20px;
}

body.theme-cute .import-btn:hover {
    background: #f1f8e9;
    border-color: #66bb6a;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.3);
}

body.theme-cute .import-btn .format-icon {
    color: #4caf50;
}

body.theme-cute .import-btn .format-name {
    color: #2e7d32;
}

body.theme-cute .import-btn .format-desc {
    color: #388e3c;
}

body.theme-cute .import-info {
    background: #fff8e1;
    border: 3px solid #ffcc02;
    border-radius: 16px;
}

body.theme-cute .import-info p {
    color: #f57f17;
}

body.theme-cute .import-info ul {
    color: #f57f17;
}

body.theme-cute .reset-schedule-btn {
    background: #e91e63;
    border-radius: 12px;
}

body.theme-cute .reset-schedule-btn:hover {
    background: #c2185b;
    box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
}

body.theme-cute .export-js-btn {
    background: #00bcd4;
    border-radius: 12px;
}

body.theme-cute .export-js-btn:hover {
    background: #0097a7;
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
}

/* 響應式設計 */
@media (max-width: 768px) {
    .import-controls {
        flex-direction: column;
        gap: 20px;
    }
    
    .file-input-wrapper {
        width: 100%;
    }
    
    .import-btn {
        width: 100%;
    }
}

/* 模態對話框樣式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 0;
    width: 98%;
    max-width: 1800px; /* 進一步增加最大寬度 */
    max-height: 98vh; /* 增加高度 */
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    transform: scale(0.7);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 45px 60px; /* 增加內邊距 */
    border-bottom: 2px solid #f8f9fa;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px 20px 0 0;
}

.modal-header h2 {
    margin: 0;
    font-size: var(--modal-h2-size);
    font-weight: 700;
}

.close-button {
    background: none;
    border: none;
    color: white;
    font-size: 2.2em; /* 增加關閉按鈕大小 */
    cursor: pointer;
    padding: 0;
    width: 80px; /* 增加按鈕大小 */
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.close-button:hover,
.close-button:focus {
    background-color: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 65px; /* 增加內邊距 */
}

/* 課程表控制按鈕 */
.schedule-controls {
    display: flex;
    gap: 25px; /* 增加間距 */
    margin-bottom: 35px; /* 增加下邊距 */
    flex-wrap: wrap;
}

.control-button {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 20px 35px; /* 增加按鈕大小 */
    border-radius: 12px; /* 增加圓角 */
    cursor: pointer;
    font-size: 1.0em; /* 增加字體大小 */
    font-weight: 600;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 160px; /* 增加最小寬度 */
}

.control-button:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

.control-button.primary {
    background-color: #28a745;
}

.control-button.primary:hover {
    background-color: #218838;
}

/* 課程列表樣式 */
.schedule-list {
    max-height: 500px; /* 增加最大高度 */
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 12px; /* 增加圓角 */
    padding: 15px; /* 增加內邊距 */
}

.schedule-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 10px; /* 增加圓角 */
    padding: 20px; /* 增加內邊距 */
    margin-bottom: 15px; /* 增加下邊距 */
    transition: all 0.3s ease;
}

.schedule-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.schedule-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px; /* 增加下邊距 */
}

.schedule-item-title {
    font-weight: bold;
    font-size: var(--schedule-item-title-size);
    color: #495057;
}

.schedule-item-actions {
    display: flex;
    gap: 12px; /* 增加間距 */
}

.schedule-item-actions button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px 12px; /* 增加內邊距 */
    border-radius: 6px; /* 增加圓角 */
    font-size: 0.9em; /* 增加字體大小 */
    font-weight: 600; /* 增加字重 */
    transition: background-color 0.3s;
}

.schedule-item-actions .edit-btn {
    color: #007bff;
}

.schedule-item-actions .delete-btn {
    color: #dc3545;
}

.schedule-item-actions button:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.schedule-item-details {
    font-size: var(--schedule-item-details-size);
    color: #6c757d;
    line-height: 1.5; /* 增加行高 */
}

/* 休息時間項目特殊樣式 */
.break-time-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px dashed #dee2e6;
    position: relative;
    overflow: hidden;
}

.break-time-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    pointer-events: none;
}

.break-time-item:hover {
    background: linear-gradient(135deg, #f1f3f4 0%, #f8f9fa 100%);
    transform: translateX(3px);
    border-color: #adb5bd;
}

.break-time-item .schedule-item-header {
    border-radius: 8px;
    padding: 12px 15px;
    margin: -5px -5px 10px -5px;
}

.break-time-item .schedule-item-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.break-time-item .schedule-item-actions span {
    background: rgba(108, 117, 125, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 500;
}

/* 表單樣式 */
.form-group {
    margin-bottom: 30px; /* 增加下邊距 */
}

.form-group label {
    display: block;
    margin-bottom: 15px; /* 增加下邊距 */
    font-weight: 700;
    color: #495057;
    font-size: var(--form-label-size);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 18px; /* 增加內邊距 */
    border: 3px solid #dee2e6;
    border-radius: 12px; /* 增加圓角 */
    font-size: var(--form-input-size);
    font-weight: 500;
    transition: border-color 0.3s;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 5px rgba(102, 126, 234, 0.1); /* 增加陰影 */
}

.form-group textarea {
    resize: vertical;
    min-height: 140px; /* 增加最小高度 */
}

.form-actions {
    display: flex;
    gap: 25px; /* 增加間距 */
    justify-content: flex-end;
    margin-top: 40px; /* 增加上邊距 */
    padding-top: 30px; /* 增加上邊距 */
    border-top: 2px solid #dee2e6;
}

.form-actions .button {
    padding: 18px 35px; /* 增加按鈕大小 */
    font-size: 1.0em; /* 增加字體大小 */
    font-weight: 600;
    border-radius: 12px; /* 增加圓角 */
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-actions .button.secondary {
    background-color: #6c757d;
    color: white;
    border: none;
}

.form-actions .button.secondary:hover {
    background-color: #5a6268;
}

.form-actions .button.primary {
    background-color: #28a745;
    color: white;
    border: none;
}

.form-actions .button.primary:hover {
    background-color: #218838;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .container {
        width: 95%;
        padding: 20px;
    }
    
    .font-size-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .font-size-buttons {
        justify-content: center;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .schedule-controls {
        flex-direction: column;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .edit-button {
        padding: 12px 24px;
        font-size: 1.1em;
    }
}

/* 大螢幕適配 */
@media (min-width: 1400px) {
    .container {
        max-width: 2000px; /* 增加最大寬度 */
        padding: 70px; /* 增加內邊距 */
    }
    
    .modal-content {
        max-width: 2000px; /* 增加最大寬度 */
    }
    
    .modal-body {
        padding: 75px; /* 增加內邊距 */
    }
}

/* 超大螢幕適配 */
@media (min-width: 1920px) {
    .container {
        max-width: 2200px; /* 增加最大寬度 */
        padding: 80px; /* 增加內邊距 */
    }
    
    .modal-content {
        max-width: 2200px; /* 增加最大寬度 */
    }
    
    .modal-body {
        padding: 85px; /* 增加內邊距 */
    }
}

/* 超寬螢幕適配 */
@media (min-width: 2560px) {
    .container {
        max-width: 2400px; /* 增加最大寬度 */
        padding: 90px; /* 增加內邊距 */
    }
    
    .modal-content {
        max-width: 2400px; /* 增加最大寬度 */
    }
    
    .modal-body {
        padding: 95px; /* 增加內邊距 */
    }
}

/* Header 新佈局樣式 */
header {
    text-align: center;
    margin-bottom: 30px;
}

.header-main {
    text-align: center;
}

/* 科技感控制項樣式 */
.header-controls {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 20px;
    padding: 25px;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    margin-top: 30px;
    position: relative;
    overflow: hidden;
}

/* 主題切換控制項沿用 compact 風格 */
.theme-controls.compact {
    display: flex;
    align-items: center;
    gap: 10px;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 12px 16px;
    box-shadow: var(--glow-cyan);
}

.theme-controls .theme-buttons {
    display: flex;
    gap: 6px;
}

/* 測試時間控制項 */
.test-controls {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    margin-bottom: 8px;
}

.time-test-controls.compact {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 107, 107, 0.5);
    border-radius: 16px;
    padding: 15px 20px;
    box-shadow: 0 0 30px rgba(255, 107, 107, 0.4);
}

.time-test-controls .control-icon {
    font-size: 1.5em;
    color: #ff6b6b;
    text-shadow: 0 0 10px rgba(255, 107, 107, 0.8);
}

.time-test-buttons {
    display: flex;
    gap: 6px;
}

.time-test-buttons .music-btn {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    min-width: 100px;
}

.time-test-buttons .music-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(255, 71, 87, 0.6);
    background: linear-gradient(135deg, #ff3742, #ff2d3a);
}

.time-test-buttons .music-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 10px rgba(255, 71, 87, 0.4);
}

/* 可愛風格下的測試時間控制項 */
body.theme-cute .time-test-controls.compact {
    background: rgba(255, 255, 255, 0.95);
    border: 3px solid #ff9ec7;
    box-shadow: 0 6px 25px rgba(255, 158, 199, 0.4);
}

body.theme-cute .time-test-controls .control-icon {
    color: #ff6b9d;
    text-shadow: 0 0 10px rgba(255, 107, 157, 0.8);
}

body.theme-cute .time-test-buttons .music-btn {
    background: linear-gradient(135deg, #ff6b9d, #ff4081);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.theme-cute .time-test-buttons .music-btn:hover {
    background: linear-gradient(135deg, #ff4081, #e91e63);
    transform: translateY(-3px);
    box-shadow: 0 6px 25px rgba(255, 64, 129, 0.5);
}

/* 底部控制項容器 */
.footer-controls {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    margin-bottom: 8px;
}

/* 在可愛風下，主題切換按鈕也呈現可愛膠囊 */
body.theme-cute .theme-controls.compact {
    background: #fff;
    border: 3px solid #ffeef6;
}
body.theme-cute #theme-toggle-btn {
    background: linear-gradient(180deg, #e5ddff, #d7c8ff);
    color: #5a3fbf;
    border: 2px solid #d7c8ff;
}

.header-controls::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
    opacity: 0.6;
}

.controls-section {
    margin-top: 25px;
}

/* 科技感緊湊型控制項 */
.font-size-controls.compact,
.music-controls.compact {
    display: flex;
    align-items: center;
    gap: 10px;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    padding: 12px 16px;
    box-shadow: var(--glow-cyan);
    margin: 0;
    transition: all 0.3s ease;
}

.font-size-controls.compact:hover,
.music-controls.compact:hover {
    border-color: var(--accent-cyan);
    box-shadow: var(--glow-cyan), 0 5px 15px rgba(0, 212, 255, 0.2);
}

.control-icon {
    font-size: 1.3em;
    color: var(--accent-cyan);
    cursor: help;
    filter: drop-shadow(0 0 5px var(--accent-cyan));
}

/* 科技感緊湊型按鈕 */
.font-size-controls.compact .font-size-buttons,
.music-controls.compact .music-buttons {
    display: flex;
    gap: 6px;
    background: none;
    border: none;
    padding: 0;
}

.font-size-controls.compact .font-size-btn,
.music-controls.compact .music-btn {
    background: var(--glass-bg);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
    padding: 8px 12px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 0.75em;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 40px;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.font-size-controls.compact .font-size-btn.active {
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    color: var(--text-accent);
    border-color: var(--accent-cyan);
    box-shadow: var(--glow-cyan);
}

.music-controls.compact .music-btn {
    background: linear-gradient(135deg, var(--accent-pink), var(--accent-purple));
    color: var(--text-accent);
    border-color: var(--accent-pink);
}

.font-size-controls.compact .font-size-btn:hover,
.music-controls.compact .music-btn:hover {
    transform: translateY(-2px) scale(1.05);
    border-color: var(--accent-cyan);
    box-shadow: var(--glow-cyan);
    color: var(--text-accent);
}

.music-controls.compact .music-btn:hover {
    border-color: var(--accent-pink);
    box-shadow: var(--glow-pink);
}

/* 測試音樂按鈕樣式 */
.test-music-btn {
    background: linear-gradient(135deg, var(--accent-green), var(--accent-cyan));
    color: var(--text-accent);
    border: 1px solid var(--glass-border);
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.8em;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    backdrop-filter: blur(10px);
    min-width: 60px;
}

.test-music-btn:hover {
    transform: translateY(-2px) scale(1.05);
    border-color: var(--accent-green);
    box-shadow: var(--glow-cyan);
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-green));
}

.test-music-btn:active {
    transform: translateY(0) scale(1);
}

.font-size-controls.compact .font-size-btn.active {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    box-shadow: 0 2px 6px rgba(72, 187, 120, 0.4);
}

/* 課程左右併排佈局 */
.course-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
    margin-bottom: 30px;
}

.current-course {
    flex: 1;
    min-width: 0; /* 允許 flex 項目縮小 */
}

.next-course {
    flex: 1;
    min-width: 0; /* 允許 flex 項目縮小 */
}

.next-course .card {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(124, 119, 198, 0.1));
    border-left: 4px solid var(--accent-cyan);
    box-shadow: var(--glow-cyan);
}

.next-course .card h2 {
    color: var(--accent-cyan);
    text-shadow: var(--glow-cyan);
}

.next-course .subject {
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 響應式設計 */
@media (max-width: 480px) {
    .header-controls {
        flex-wrap: wrap;
        padding: 15px;
    }

    .font-size-controls.compact,
    .music-controls.compact {
        flex: 1;
        justify-content: center;
        min-width: 150px;
    }

    /* 移除課程垂直排列，保持左右併排 */
    /* .course-layout {
        flex-direction: column;
        gap: 20px;
        margin-bottom: 20px;
    } */
}

/* 科技感動畫效果 */
@keyframes dataFlow {
    0% { transform: translateX(-100%); opacity: 0; }
    50% { opacity: 1; }
    100% { transform: translateX(100%); opacity: 0; }
}

@keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--accent-cyan);
    }
    50% {
        box-shadow: 0 0 20px var(--accent-cyan), 0 0 30px var(--accent-cyan);
    }
}

/* 科技感文字效果 */
.tech-text {
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: pulse 2s infinite;
}

/* 科技感邊框動畫 */
.tech-border {
    position: relative;
    overflow: hidden;
}

.tech-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
    animation: dataFlow 3s infinite;
}

/* 科技感滾動條 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--accent-cyan), var(--accent-purple));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--accent-purple), var(--accent-pink));
}
