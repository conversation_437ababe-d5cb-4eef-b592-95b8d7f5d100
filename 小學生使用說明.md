# 📚 課程提醒小幫手 - 小學生版使用說明

## 🎯 專為小學生設計

這個版本特別為小學生優化，介面簡潔，沒有複雜的開發工具。

## 🚀 如何啟動

### 方式一：桌面快捷方式（最簡單）
1. 找到桌面上的「課程提醒小幫手」圖標
2. 雙擊圖標就能啟動

### 方式二：檔案啟動
雙擊以下任一檔案：
- `小學生版.bat` - 最簡潔版本
- `小學生啟動.bat` - 有提示訊息版本

## 📱 使用方法

### 第一次使用
1. 啟動應用後會看到課程表
2. 點擊「🎵」按鈕可以設定音樂
3. 點擊「🔤」按鈕可以調整字體大小

### 日常使用
- **查看課程**：主畫面顯示現在和下一節課
- **設定音樂**：點擊音樂按鈕，選擇喜歡的鈴聲
- **調整字體**：點擊字體按鈕，選擇看得舒服的大小
- **關閉應用**：點擊右上角的 X，應用會縮小到右下角

## 🎵 音樂設定

### 可以設定的音樂
- **上課鈴聲**：上課時播放的音樂
- **倒數提醒**：準備上課前的提醒音
- **特殊時段**：午餐、午休、放學時的背景音樂

### 音樂選項
- 不播放（安靜模式）
- 經典鈴聲
- 輕柔鐘聲
- 清脆鈴聲
- 自訂音樂（可以上傳自己的音樂）

## 🔤 字體大小

有五種字體大小可選：
- **極小**：適合大螢幕
- **小**：適合一般使用
- **中**：預設大小，最舒適
- **大**：適合看不清楚時
- **超大**：適合坐得比較遠

## 💡 小提醒

### 如果應用不見了
1. 看看右下角的系統托盤（時鐘附近）
2. 找到課程提醒的小圖標
3. 點擊一下就會重新顯示

### 如果需要幫助
1. 問問老師或家長
2. 重新啟動應用
3. 檢查電腦音量是否開啟

### 設定會自動保存
- 不用擔心設定會消失
- 重新開機後設定還在
- 可以安心關閉電腦

## 🎒 給老師和家長

### 安全特色
- 沒有網路連線需求
- 所有資料儲存在本機
- 簡潔的使用介面
- 適合教育環境使用

### 教學用途
- 幫助學生養成時間觀念
- 培養準時上課的習慣
- 練習使用電腦軟體
- 學習自主管理時間

## 📞 問題排除

### 應用啟動失敗
1. 確認電腦已安裝 Node.js
2. 找老師或技術人員協助
3. 重新安裝應用

### 沒有聲音
1. 檢查電腦音量
2. 檢查音樂設定是否為「不播放」
3. 嘗試選擇其他音效

### 字體太小或太大
1. 點擊「🔤」按鈕
2. 選擇適合的字體大小
3. 設定會自動保存

## 🌟 享受學習時光

這個小工具會幫助你：
- 準時上每一節課
- 不會忘記帶課本
- 養成良好的時間習慣
- 讓學習更有趣

**祝你學習愉快！** 📚✨
