const { app, BrowserWindow, <PERSON>u, Tray, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');

// 保持對視窗的全域引用
let mainWindow;
let tray = null;
let isQuitting = false;

// 開發模式檢測
const isDev = process.argv.includes('--dev');

// 設定檔案路徑
const userDataPath = app.getPath('userData');
const settingsPath = path.join(userDataPath, 'settings.json');
const schedulePath = path.join(userDataPath, 'schedule.json');

// 確保用戶資料目錄存在
if (!fs.existsSync(userDataPath)) {
    fs.mkdirSync(userDataPath, { recursive: true });
}

function createWindow() {
    // 創建瀏覽器視窗
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 900,
        minWidth: 800,
        minHeight: 600,
        // icon: path.join(__dirname, 'assets', 'icon.png'), // 暫時註解避免載入錯誤
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: true,
            allowRunningInsecureContent: false
        },
        show: false, // 初始不顯示，等載入完成後再顯示
        titleBarStyle: 'default',
        autoHideMenuBar: !isDev // 生產環境隱藏選單列
    });

    // 載入應用的 index.html
    mainWindow.loadFile('index.html');

    // 當視窗準備好時顯示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // 開發模式下可選擇性打開 DevTools（小學生模式：不打開）
        // if (isDev) {
        //     mainWindow.webContents.openDevTools();
        // }
    });

    // 視窗關閉事件處理
    mainWindow.on('close', (event) => {
        if (!isQuitting) {
            event.preventDefault();
            mainWindow.hide();
            
            // 首次最小化到系統托盤時顯示提示
            if (!app.getLoginItemSettings().wasOpenedAsHidden) {
                tray.displayBalloon({
                    icon: path.join(__dirname, 'assets', 'icon.png'),
                    title: '課程提醒小幫手',
                    content: '應用已最小化到系統托盤，點擊托盤圖標可以重新開啟'
                });
            }
        }
    });

    // 視窗被關閉時
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // 攔截外部連結，用系統瀏覽器打開
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

function createTray() {
    // 創建系統托盤
    let iconPath = path.join(__dirname, 'assets', 'icon.png');
    
    // 檢查圖標文件是否存在，如果不存在則使用系統默認圖標
    if (!fs.existsSync(iconPath)) {
        console.log('Icon file not found, using default icon');
        // 創建一個 16x16 的簡單圖標
        iconPath = require('electron').nativeImage.createEmpty();
    }
    
    try {
        tray = new Tray(iconPath);
    } catch (error) {
        console.log('Failed to create tray, using empty icon');
        tray = new Tray(require('electron').nativeImage.createEmpty());
    }
    
    const contextMenu = Menu.buildFromTemplate([
        {
            label: '顯示主視窗',
            click: () => {
                showWindow();
            }
        },
        {
            label: '檢查課程',
            click: () => {
                showWindow();
                // 通知渲染進程檢查課程
                if (mainWindow) {
                    mainWindow.webContents.send('check-schedule');
                }
            }
        },
        {
            type: 'separator'
        },
        {
            label: '開機自動啟動',
            type: 'checkbox',
            checked: app.getLoginItemSettings().openAtLogin,
            click: (item) => {
                app.setLoginItemSettings({
                    openAtLogin: item.checked,
                    openAsHidden: true
                });
            }
        },
        {
            type: 'separator'
        },
        {
            label: '關於',
            click: () => {
                dialog.showMessageBox(mainWindow, {
                    type: 'info',
                    title: '關於課程提醒小幫手',
                    message: '課程提醒小幫手 v1.0.0',
                    detail: '一個智能的課程時間管理工具，幫助您準時參加每一節課。\n\n功能特色：\n• 即時課程提醒\n• 任務清單管理\n• 自動視窗喚醒\n• 系統托盤支援',
                    buttons: ['確定']
                });
            }
        },
        {
            label: '退出',
            click: () => {
                isQuitting = true;
                app.quit();
            }
        }
    ]);
    
    tray.setToolTip('課程提醒小幫手');
    tray.setContextMenu(contextMenu);
    
    // 點擊托盤圖標顯示/隱藏視窗
    tray.on('click', () => {
        if (mainWindow.isVisible()) {
            mainWindow.hide();
        } else {
            showWindow();
        }
    });
    
    // 雙擊托盤圖標顯示視窗
    tray.on('double-click', () => {
        showWindow();
    });
}

function showWindow() {
    if (mainWindow) {
        if (mainWindow.isMinimized()) {
            mainWindow.restore();
        }
        mainWindow.show();
        mainWindow.focus();
        
        // 將視窗置於最前
        mainWindow.setAlwaysOnTop(true);
        mainWindow.setAlwaysOnTop(false);
    }
}

// 處理來自渲染進程的消息
ipcMain.handle('show-window', () => {
    showWindow();
});

ipcMain.handle('hide-window', () => {
    if (mainWindow) {
        mainWindow.hide();
    }
});

ipcMain.handle('minimize-window', () => {
    if (mainWindow) {
        mainWindow.minimize();
    }
});

ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

// 處理課程開始通知
ipcMain.handle('class-started', (event, classInfo) => {
    // 顯示視窗
    showWindow();
    
    // 顯示系統托盤氣球通知
    if (tray && !mainWindow.isVisible()) {
        tray.displayBalloon({
            icon: path.join(__dirname, 'assets', 'icon.png'),
            title: `上課時間到了！${classInfo.subject}`,
            content: `請準備：${classInfo.tasks.slice(0, 2).join(', ')}${classInfo.tasks.length > 2 ? '...' : ''}`
        });
    }
});

// 設定檔案管理功能
ipcMain.handle('save-settings', async (event, settings) => {
    try {
        const settingsData = {
            ...settings,
            lastModified: new Date().toISOString()
        };
        await fs.promises.writeFile(settingsPath, JSON.stringify(settingsData, null, 2), 'utf8');
        console.log('設定已儲存到:', settingsPath);
        return { success: true };
    } catch (error) {
        console.error('儲存設定失敗:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('load-settings', async () => {
    try {
        if (fs.existsSync(settingsPath)) {
            const data = await fs.promises.readFile(settingsPath, 'utf8');
            const settings = JSON.parse(data);
            console.log('設定已從檔案載入:', settingsPath);
            return { success: true, data: settings };
        } else {
            console.log('設定檔案不存在，使用預設設定');
            return { success: true, data: null };
        }
    } catch (error) {
        console.error('載入設定失敗:', error);
        return { success: false, error: error.message };
    }
});

// 課表檔案管理功能
ipcMain.handle('save-schedule', async (event, schedule) => {
    try {
        const scheduleData = {
            schedule: schedule,
            lastModified: new Date().toISOString()
        };
        await fs.promises.writeFile(schedulePath, JSON.stringify(scheduleData, null, 2), 'utf8');
        console.log('課表已儲存到:', schedulePath);
        return { success: true };
    } catch (error) {
        console.error('儲存課表失敗:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('load-schedule', async () => {
    try {
        if (fs.existsSync(schedulePath)) {
            const data = await fs.promises.readFile(schedulePath, 'utf8');
            const scheduleData = JSON.parse(data);
            console.log('課表已從檔案載入:', schedulePath);
            return { success: true, data: scheduleData.schedule };
        } else {
            console.log('課表檔案不存在，使用預設課表');
            return { success: true, data: null };
        }
    } catch (error) {
        console.error('載入課表失敗:', error);
        return { success: false, error: error.message };
    }
});

// 獲取設定檔案路徑
ipcMain.handle('get-settings-path', () => {
    return {
        settingsPath: settingsPath,
        schedulePath: schedulePath,
        userDataPath: userDataPath
    };
});

// 當 Electron 完成初始化並準備創建瀏覽器視窗時調用此方法
app.whenReady().then(() => {
    createWindow();
    createTray();
    
    // macOS 特定處理
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createWindow();
        } else if (mainWindow) {
            showWindow();
        }
    });
});

// 當所有視窗都被關閉時退出應用
app.on('window-all-closed', () => {
    // macOS 上，除非用戶按 Cmd + Q 顯式退出，否則應用和選單列會保持活動狀態
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('before-quit', () => {
    isQuitting = true;
});

// 處理協議註冊（可選）
if (process.defaultApp) {
    if (process.argv.length >= 2) {
        app.setAsDefaultProtocolClient('class-reminder', process.execPath, [path.resolve(process.argv[1])]);
    }
} else {
    app.setAsDefaultProtocolClient('class-reminder');
}

// 安全設置
app.on('web-contents-created', (event, contents) => {
    contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
    });
});