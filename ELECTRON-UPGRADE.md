# 🚀 Electron 設定儲存升級說明

## ✅ 升級完成

您的課程提醒小幫手已成功升級為 Electron 應用，設定現在會永久保存在檔案系統中，不會再跑掉！

## 🔧 升級內容

### 主要改進
1. **永久設定儲存** - 設定現在儲存在檔案系統中，重啟應用或清除瀏覽器資料都不會影響
2. **音樂設定保護** - 音樂設定會即時儲存到檔案，避免設定丟失
3. **字體設定保護** - 字體大小設定也會永久保存
4. **降級支援** - 在瀏覽器環境中仍可使用 localStorage 作為後備方案

### 技術細節
- **設定檔案位置**: `%APPDATA%/class-reminder/settings.json` (Windows)
- **課表檔案位置**: `%APPDATA%/class-reminder/schedule.json` (Windows)
- **自動備份**: 每次儲存都會包含時間戳記
- **錯誤處理**: 檔案操作失敗時會自動降級到 localStorage

## 🎯 使用方法

### 啟動 Electron 版本
```bash
# 開發模式啟動
npm run dev

# 或正常啟動
npm start
```

### 編譯成執行檔
```bash
# Windows 版本
npm run build-win

# macOS 版本
npm run build-mac

# Linux 版本
npm run build-linux
```

## 📂 設定檔案位置

### Windows
```
C:\Users\<USER>\AppData\Roaming\class-reminder\
├── settings.json    # 應用設定（音樂、字體等）
└── schedule.json    # 課表資料
```

### macOS
```
~/Library/Application Support/class-reminder/
├── settings.json
└── schedule.json
```

### Linux
```
~/.config/class-reminder/
├── settings.json
└── schedule.json
```

## 🔄 移轉說明

如果您之前已經在瀏覽器中設定過音樂或字體：

1. **首次啟動** Electron 版本時，會自動使用預設設定
2. **重新設定** 一次您的偏好設定
3. **之後的設定** 都會永久保存在檔案中

## 🎵 音樂設定改進

- **預設為靜音** - 新安裝時不會有嗶聲干擾
- **設定保護** - 取消或關閉設定對話框時會重置為已保存的設定
- **即時保存** - 點擊「儲存設定」後立即寫入檔案

## 🐛 問題排除

### 如果設定沒有保存
1. 檢查應用是否有寫入檔案的權限
2. 查看控制台是否有錯誤訊息
3. 嘗試以管理員身分執行

### 如果無法啟動
1. 確認已安裝 Node.js
2. 執行 `npm install` 安裝依賴
3. 檢查 Electron 版本是否相容

## 📋 測試清單

- ✅ 音樂設定儲存/載入
- ✅ 字體設定儲存/載入
- ✅ 設定對話框取消/關閉時重置
- ✅ 瀏覽器環境降級支援
- ✅ 錯誤處理和日誌記錄

現在您的課程提醒小幫手會永久記住您的設定！🎉
