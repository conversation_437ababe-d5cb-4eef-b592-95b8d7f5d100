<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倒數計時器修復測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .countdown-display {
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>⏱️ 倒數計時器修復測試</h1>
        <p class="info">這個頁面用來測試倒數計時器的修復是否成功，確保它不會被重複啟動。</p>
        
        <div class="countdown-display" id="countdown-display">2:00</div>
        
        <div class="status" id="status">
            ⏳ 倒數計時器已準備就緒
        </div>
        
        <div class="controls">
            <button id="start-btn">開始倒數</button>
            <button id="stop-btn" disabled>停止倒數</button>
            <button id="reset-btn">重置</button>
            <button id="simulate-check">模擬課程檢查</button>
        </div>
        
        <div>
            <label for="minutes-input">設定分鐘數：</label>
            <input type="number" id="minutes-input" min="1" max="10" value="2" style="width: 60px;">
            <button id="set-minutes">設定</button>
        </div>
        
        <h3>📋 測試日誌：</h3>
        <div class="log" id="log"></div>
        
        <div style="margin-top: 20px;">
            <h3>🔍 測試說明：</h3>
            <ul>
                <li><strong>開始倒數</strong>：啟動倒數計時器</li>
                <li><strong>模擬課程檢查</strong>：模擬每5秒執行的課程檢查，測試是否會重複啟動倒數計時器</li>
                <li><strong>預期行為</strong>：倒數計時器應該正常倒數，不會被重複啟動而重置</li>
            </ul>
        </div>
    </div>

    <script>
        // 模擬倒數計時器邏輯
        let countdownInterval = null;
        let countdownMinutes = 2;
        let isCountdownRunning = false;
        let currentCountdownClassId = null; // 模擬課程ID追蹤
        let simulateInterval = null;
        
        const countdownDisplay = document.getElementById('countdown-display');
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        const startBtn = document.getElementById('start-btn');
        const stopBtn = document.getElementById('stop-btn');
        const resetBtn = document.getElementById('reset-btn');
        const simulateBtn = document.getElementById('simulate-check');
        const minutesInput = document.getElementById('minutes-input');
        const setMinutesBtn = document.getElementById('set-minutes');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `[${timestamp}] ${message}<br>`;
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function startCountdown() {
            // 修復：如果倒數計時器已經在運行，不要重新啟動
            if (isCountdownRunning) {
                log('⚠️ 倒數計時器已在運行，跳過重複啟動');
                return;
            }
            
            // 清除之前的計時器
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            
            log('▶️ 啟動倒數計時器');
            updateStatus('⏳ 倒數計時進行中...', 'info');
            
            // 設置倒數時間（轉換為秒）
            let timeLeft = countdownMinutes * 60;
            isCountdownRunning = true;
            
            // 更新按鈕狀態
            startBtn.disabled = true;
            stopBtn.disabled = false;
            
            // 開始倒數計時
            countdownInterval = setInterval(() => {
                timeLeft--;
                
                // 計算分鐘和秒數
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                
                // 更新顯示
                countdownDisplay.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
                
                // 當時間到0時停止計時
                if (timeLeft <= 0) {
                    clearInterval(countdownInterval);
                    countdownInterval = null;
                    isCountdownRunning = false;
                    countdownDisplay.textContent = '時間到！';
                    updateStatus('✅ 倒數計時完成！', 'success');
                    log('🎉 倒數計時完成');
                    
                    // 更新按鈕狀態
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                }
            }, 1000);
        }
        
        function stopCountdown() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            isCountdownRunning = false;
            currentCountdownClassId = null; // 重置課程ID
            log('⏹️ 停止倒數計時器');
            updateStatus('⏹️ 倒數計時已停止', 'info');

            // 更新按鈕狀態
            startBtn.disabled = false;
            stopBtn.disabled = true;
        }
        
        function resetCountdown() {
            stopCountdown();
            countdownDisplay.textContent = `${countdownMinutes}:00`;
            updateStatus('🔄 倒數計時器已重置', 'info');
            log('🔄 重置倒數計時器');
        }
        
        function simulateScheduleCheck() {
            log('🔍 執行課程檢查（模擬）');
            // 模擬課程檢查時會調用 startCountdown，但只有在課程變化時才啟動
            const mockClassId = 1; // 模擬固定的課程ID

            if (currentCountdownClassId !== mockClassId) {
                currentCountdownClassId = mockClassId;
                log('📚 檢測到新課程，啟動倒數計時器');
                startCountdown();
            } else {
                log('📚 同一課程，跳過倒數計時器啟動');
            }
        }
        
        // 事件監聽器
        startBtn.addEventListener('click', startCountdown);
        stopBtn.addEventListener('click', stopCountdown);
        resetBtn.addEventListener('click', resetCountdown);
        
        simulateBtn.addEventListener('click', () => {
            if (simulateInterval) {
                clearInterval(simulateInterval);
                simulateInterval = null;
                simulateBtn.textContent = '模擬課程檢查';
                log('⏹️ 停止模擬課程檢查');
            } else {
                simulateInterval = setInterval(simulateScheduleCheck, 3000); // 每3秒模擬一次
                simulateBtn.textContent = '停止模擬';
                log('▶️ 開始模擬課程檢查（每3秒一次）');
            }
        });
        
        setMinutesBtn.addEventListener('click', () => {
            const minutes = parseInt(minutesInput.value, 10);
            if (minutes > 0 && minutes <= 10) {
                countdownMinutes = minutes;
                resetCountdown();
                log(`⚙️ 設定倒數時間為 ${minutes} 分鐘`);
            }
        });
        
        // 初始化
        log('🚀 倒數計時器測試頁面已載入');
        updateStatus('⏳ 倒數計時器已準備就緒', 'info');
    </script>
</body>
</html>
