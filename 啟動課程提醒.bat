@echo off
chcp 65001 >nul
title 課程提醒小幫手
echo.
echo 🚀 正在啟動課程提醒小幫手...
echo.

cd /d "%~dp0"

REM 檢查 Node.js 是否安裝
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤：未找到 Node.js
    echo 請先安裝 Node.js：https://nodejs.org/
    pause
    exit /b 1
)

REM 檢查 npm 是否可用
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 錯誤：npm 無法使用
    pause
    exit /b 1
)

REM 檢查依賴是否安裝
if not exist "node_modules" (
    echo 📦 首次運行，正在安裝依賴...
    npm install
    if errorlevel 1 (
        echo ❌ 依賴安裝失敗
        pause
        exit /b 1
    )
)

echo ✅ 環境檢查完成，啟動應用...
echo.
echo 💡 提示：關閉此視窗將結束應用
echo      應用會最小化到系統托盤
echo.

REM 啟動應用
npm run dev

pause
