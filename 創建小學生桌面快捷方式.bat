@echo off
chcp 65001 >nul
echo 🎒 正在為小朋友創建桌面快捷方式...

set "desktop=%USERPROFILE%\Desktop"
set "target=%~dp0📚 小學生啟動.bat"
set "shortcut=%desktop%\📚 課程提醒小幫手（小學生版）.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut%'); $Shortcut.TargetPath = '%target%'; $Shortcut.WorkingDirectory = '%~dp0'; $Shortcut.Description = '課程提醒小幫手 - 小學生專用版'; $Shortcut.Save()"

if exist "%shortcut%" (
    echo ✅ 小學生版桌面快捷方式創建成功！
    echo 📍 位置: %shortcut%
    echo.
    echo 🎯 現在小朋友可以雙擊桌面上的圖標來使用囉！
    echo 💡 這個版本不會顯示複雜的開發工具
) else (
    echo ❌ 快捷方式創建失敗
)

echo.
pause
