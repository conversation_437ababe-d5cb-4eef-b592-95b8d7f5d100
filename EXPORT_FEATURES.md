# 課表匯出功能說明

## 功能概述

課程提醒小幫手現在支援多種格式的課表匯出功能，讓您可以輕鬆將課表資料備份或分享。

## 支援的匯出格式

### 1. JSON 格式 📄
- **用途**: 原始資料格式，適合程式開發使用
- **特色**: 保留完整的資料結構，包含所有欄位資訊
- **檔案大小**: 較小，適合儲存和傳輸

### 2. CSV 格式 📊
- **用途**: 試算表格式，可用 Excel、Google Sheets 開啟
- **特色**: 表格化資料，方便進行資料分析
- **相容性**: 支援中文，可在各種試算表軟體中開啟

### 3. PDF 格式 📋
- **用途**: 可列印文件，適合製作紙本課表
- **特色**: 格式固定，適合列印和分享
- **布局**: 按天分組，包含時間、科目和任務清單

### 4. Excel 格式 📈
- **用途**: Excel 工作表，功能最豐富
- **特色**: 可編輯，支援進階的試算表功能
- **欄寬**: 自動調整，方便閱讀和編輯

## 匯出選項

### 包含週末課程
- ✅ 勾選：匯出包含週六、週日的課程
- ❌ 取消勾選：只匯出週一到週五的課程

### 包含任務清單
- ✅ 勾選：匯出每個課程的詳細任務清單
- ❌ 取消勾選：只匯出基本的課程資訊（時間、科目）

### 包含特殊時段
- ✅ 勾選：匯出特殊時段（掃地、午餐、午休、放學準備）
- ❌ 取消勾選：只匯出一般課程

### 自訂檔案名稱
- 預設：「課程表」
- 可自訂任何名稱，系統會自動添加對應的副檔名

## 使用方法

1. 點擊主頁面的「📤 匯出課表」按鈕
2. 在彈出的視窗中選擇匯出格式
3. 調整匯出選項（週末、任務清單、特殊時段）
4. 輸入自訂檔案名稱（可選）
5. 點擊對應的格式按鈕即可下載

## 技術特點

- **動態載入**: PDF 和 Excel 功能使用動態載入，不影響頁面載入速度
- **中文支援**: 所有格式都完整支援中文字元
- **錯誤處理**: 包含完整的錯誤處理和使用者提示
- **響應式設計**: 匯出介面支援各種螢幕尺寸

## 匯出資料範例

### JSON 格式範例
```json
[
  {
    "id": 1,
    "day": "Monday",
    "start": "08:05",
    "end": "08:25",
    "subject": "導師時間",
    "tasks": [
      "準時到校",
      "準備晨會",
      "整理書包和課本"
    ]
  }
]
```

### CSV 格式範例
```csv
ID,星期,開始時間,結束時間,科目,類型,任務清單
1,星期一,08:05,08:25,導師時間,一般課程,準時到校 | 準備晨會 | 整理書包和課本
```

## 瀏覽器相容性

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## 故障排除

### PDF 匯出失敗
- 檢查網路連線
- 確認瀏覽器支援動態載入 JavaScript

### Excel 匯出失敗
- 檢查網路連線
- 嘗試重新整理頁面後再試

### 中文顯示問題
- CSV 檔案請使用支援 UTF-8 的編輯器開啟
- Excel 中如果出現亂碼，請選擇「匯入」功能並指定 UTF-8 編碼

## 🔄 課表匯入功能

### 新增功能：匯入課表
現在您可以將之前匯出的課表重新匯入，讓修改的課表可以在不同電腦間保持一致！

### 匯入步驟
1. 點擊「📤 匯出課表」按鈕開啟匯出視窗
2. 在匯入區域點擊「📁 選擇檔案」按鈕
3. 選擇之前匯出的 JSON 課表檔案
4. 系統會自動驗證檔案格式
5. 確認匯入後，課表會立即更新

### 重要特點
- **自動儲存**: 匯入的課表會自動儲存到瀏覽器的 localStorage
- **跨電腦使用**: 匯出檔案可以在其他電腦上匯入使用
- **資料驗證**: 匯入時會檢查檔案格式，確保資料正確
- **可復原**: 提供「🔄 恢復原始課表」按鈕，可隨時回到原始狀態

### 持久化機制
- **瀏覽器儲存**: 匯入的課表會自動儲存在瀏覽器的 localStorage 中
- **自動載入**: 下次開啟網頁時會自動載入之前匯入的課表
- **持續保存**: 即使重新整理頁面，匯入的課表也會保持
- **可復原**: 使用「🔄 恢復原始課表」功能可清除匯入的資料

### 🆕 永久保存功能
為了解決 `schedule-data.js` 檔案不會被修改的問題，我們新增了「📜 匯出為 JS 檔案」功能：

#### 使用步驟：
1. 匯入並修改課表後
2. 點擊「📜 匯出為 JS 檔案」按鈕
3. 系統會下載新的 `schedule-data.js` 檔案
4. 手動替換專案中的原始 `schedule-data.js` 檔案
5. 重新整理網頁即可看到永久的修改

#### 優點：
- **真正永久**: 修改會寫入原始檔案中
- **跨電腦同步**: 替換檔案後在任何電腦都能看到修改
- **版本控制友善**: 可以提交到 Git 等版本控制系統
- **備份安全**: 建議先備份原始檔案

## 更新日誌

### v1.2.0 (2024-12-19)
- ✨ 新增「📜 匯出為 JS 檔案」功能
- ✨ 支援永久修改 schedule-data.js 檔案
- ✨ 自動生成格式化的 JavaScript 檔案
- 📝 新增詳細的檔案替換說明
- 🔧 改善永久保存的工作流程

### v1.1.0 (2024-12-19)
- ✨ 新增 JSON 課表匯入功能
- ✨ 新增課表資料驗證和錯誤處理
- ✨ 新增 localStorage 持久化儲存
- ✨ 新增恢復原始課表功能
- ✨ 新增檔案格式和大小檢查
- ✨ 新增匯入確認對話框
- 🔧 改善使用者體驗和錯誤提示

### v1.0.0 (2024-12-19)
- ✨ 新增 JSON 格式匯出
- ✨ 新增 CSV 格式匯出
- ✨ 新增 PDF 格式匯出
- ✨ 新增 Excel 格式匯出
- ✨ 新增匯出選項設定
- ✨ 新增自訂檔案名稱功能
- ✨ 新增特殊時段匯出
