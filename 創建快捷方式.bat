@echo off
chcp 65001 >nul
echo 正在創建桌面快捷方式...

set "desktop=%USERPROFILE%\Desktop"
set "target=%~dp0啟動課程提醒.bat"
set "shortcut=%desktop%\課程提醒小幫手.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut%'); $Shortcut.TargetPath = '%target%'; $Shortcut.WorkingDirectory = '%~dp0'; $Shortcut.Description = '課程提醒小幫手'; $Shortcut.Save()"

if exist "%shortcut%" (
    echo ✅ 桌面快捷方式創建成功！
    echo 📍 位置: %shortcut%
    echo.
    echo 💡 現在您可以雙擊桌面上的「課程提醒小幫手」圖標來啟動應用
) else (
    echo ❌ 快捷方式創建失敗
)

echo.
pause
