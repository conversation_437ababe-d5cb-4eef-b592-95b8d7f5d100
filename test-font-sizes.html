<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字體大小測試</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .font-size-demo {
            border: 2px solid #007bff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f8f9fa;
        }
        .size-label {
            background: #007bff;
            color: white;
            padding: 5px 15px;
            border-radius: 4px;
            font-weight: bold;
            margin-bottom: 15px;
            display: inline-block;
        }
        .controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button.active {
            background: #28a745;
        }
        
        /* 字體大小設定 */
        :root {
            --header-h1-size: 7.0em;
            --time-size: 6.0em;
            --card-h2-size: 5.0em;
            --subject-size: 7.6em;
            --card-h3-size: 4.0em;
            --card-li-size: 3.2em;
            --next-class-p-size: 3.6em;
            --no-class-h2-size: 5.6em;
            --no-class-p-size: 3.2em;
        }
        
        .demo-header h1 {
            font-size: var(--header-h1-size);
            margin: 0;
        }
        
        .demo-time {
            font-size: var(--time-size);
            font-weight: bold;
            color: #007bff;
        }
        
        .demo-subject {
            font-size: var(--subject-size);
            font-weight: bold;
            color: #28a745;
        }
        
        .demo-card h2 {
            font-size: var(--card-h2-size);
            margin: 10px 0;
        }
        
        .demo-card h3 {
            font-size: var(--card-h3-size);
            margin: 10px 0;
        }
        
        .demo-card li {
            font-size: var(--card-li-size);
            margin: 5px 0;
        }
        
        .demo-next-class {
            font-size: var(--next-class-p-size);
            color: #6c757d;
        }
        
        .demo-no-class h2 {
            font-size: var(--no-class-h2-size);
            margin: 10px 0;
        }
        
        .demo-no-class p {
            font-size: var(--no-class-p-size);
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔤 字體大小測試</h1>
        <p>測試課程提醒小幫手的不同字體大小設定效果。</p>
        
        <div class="controls">
            <button class="size-btn" data-size="tiny">極小</button>
            <button class="size-btn" data-size="small">小</button>
            <button class="size-btn active" data-size="medium">中</button>
            <button class="size-btn" data-size="large">大</button>
            <button class="size-btn" data-size="xlarge">超大</button>
        </div>
        
        <div class="font-size-demo">
            <div class="size-label" id="current-size">當前字體：中</div>
            
            <div class="demo-header">
                <h1>課程提醒小幫手</h1>
            </div>
            
            <div class="demo-time">14:25</div>
            
            <div class="demo-card">
                <h2>正在上課中</h2>
                <div class="demo-subject">數學</div>
                <h3>課前準備：</h3>
                <ul>
                    <li>準備數學課本和習作</li>
                    <li>拿出計算機和尺規</li>
                    <li>檢查昨天的作業</li>
                </ul>
            </div>
            
            <div class="demo-next-class">
                下一節課：英語 (15:10)
            </div>
            
            <div class="demo-no-class">
                <h2>現在是下課時間！</h2>
                <p>好好休息一下吧！</p>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>📏 字體大小說明：</h3>
            <ul>
                <li><strong>極小</strong>：適合小螢幕或需要看到更多內容時使用</li>
                <li><strong>小</strong>：比預設稍小，適合一般使用</li>
                <li><strong>中</strong>：預設大小，平衡可讀性和內容顯示</li>
                <li><strong>大</strong>：適合較大螢幕或需要更好可讀性</li>
                <li><strong>超大</strong>：適合視力較弱或遠距離觀看</li>
            </ul>
        </div>
    </div>

    <script>
        // 字體大小設定
        const fontSizeSettings = {
            tiny: {
                '--header-h1-size': '2.5em',
                '--time-size': '2.0em',
                '--card-h2-size': '1.8em',
                '--subject-size': '2.8em',
                '--card-h3-size': '1.6em',
                '--card-li-size': '1.3em',
                '--next-class-p-size': '1.5em',
                '--no-class-h2-size': '2.3em',
                '--no-class-p-size': '1.4em'
            },
            small: {
                '--header-h1-size': '3.5em',
                '--time-size': '3.0em',
                '--card-h2-size': '2.7em',
                '--subject-size': '4.2em',
                '--card-h3-size': '2.4em',
                '--card-li-size': '1.95em',
                '--next-class-p-size': '2.25em',
                '--no-class-h2-size': '3.45em',
                '--no-class-p-size': '2.1em'
            },
            medium: {
                '--header-h1-size': '7.0em',
                '--time-size': '6.0em',
                '--card-h2-size': '5.0em',
                '--subject-size': '7.6em',
                '--card-h3-size': '4.0em',
                '--card-li-size': '3.2em',
                '--next-class-p-size': '3.6em',
                '--no-class-h2-size': '5.6em',
                '--no-class-p-size': '3.2em'
            },
            large: {
                '--header-h1-size': '9.0em',
                '--time-size': '8.0em',
                '--card-h2-size': '6.4em',
                '--subject-size': '10.0em',
                '--card-h3-size': '5.0em',
                '--card-li-size': '4.0em',
                '--next-class-p-size': '4.4em',
                '--no-class-h2-size': '7.0em',
                '--no-class-p-size': '4.0em'
            },
            xlarge: {
                '--header-h1-size': '11.0em',
                '--time-size': '10.0em',
                '--card-h2-size': '8.0em',
                '--subject-size': '12.0em',
                '--card-h3-size': '6.0em',
                '--card-li-size': '5.0em',
                '--next-class-p-size': '5.6em',
                '--no-class-h2-size': '8.4em',
                '--no-class-p-size': '5.0em'
            }
        };
        
        const sizeBtns = document.querySelectorAll('.size-btn');
        const currentSizeLabel = document.getElementById('current-size');
        
        function setFontSize(size) {
            const settings = fontSizeSettings[size];
            if (settings) {
                // 更新 CSS 變數
                Object.entries(settings).forEach(([property, value]) => {
                    document.documentElement.style.setProperty(property, value);
                });
                
                // 更新按鈕狀態
                sizeBtns.forEach(btn => btn.classList.remove('active'));
                document.querySelector(`[data-size="${size}"]`).classList.add('active');
                
                // 更新標籤
                const sizeNames = {
                    tiny: '極小',
                    small: '小',
                    medium: '中',
                    large: '大',
                    xlarge: '超大'
                };
                currentSizeLabel.textContent = `當前字體：${sizeNames[size]}`;
                
                console.log(`字體大小已設定為：${size}`);
            }
        }
        
        // 事件監聽器
        sizeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const size = btn.dataset.size;
                setFontSize(size);
            });
        });
        
        // 初始化
        console.log('字體大小測試頁面已載入');
    </script>
</body>
</html>
