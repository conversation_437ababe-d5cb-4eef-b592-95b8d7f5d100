Set WshShell = CreateObject("WScript.Shell")
Set oShellLink = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\課程提醒小幫手.lnk")

strCurrentPath = CreateObject("Scripting.FileSystemObject").GetParentFolderName(WScript.ScriptFullName)

oShellLink.TargetPath = strCurrentPath & "\啟動課程提醒.bat"
oShellLink.WorkingDirectory = strCurrentPath
oShellLink.Description = "課程提醒小幫手 - 一鍵啟動"

oShellLink.Save

MsgBox "桌面快捷方式創建完成！", vbInformation, "完成"