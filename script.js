document.addEventListener('DOMContentLoaded', () => {
    // 檢查是否在 Electron 環境中
    const isElectron = typeof window !== 'undefined' && window.require;
    
    if (isElectron) {
        const { ipcRenderer } = window.require('electron');
        const fs = window.require('fs');
        const path = window.require('path');
        
        // 監聽來自主進程的課程檢查請求
        ipcRenderer.on('check-schedule', () => {
            checkSchedule();
        });
        
        // 添加視窗控制功能（可選）
        console.log('Running in Electron environment');
        
        // 將 fs 與 path 放到全域，供載入課表時使用
        window.__ELECTRON_FS__ = fs;
        window.__ELECTRON_PATH__ = path;
    }
    // --- DOM Elements ---
    const currentTimeEl = document.getElementById('current-time');
    const noClassViewEl = document.getElementById('no-class-view');
    const classViewEl = document.getElementById('class-view');
    const currentSubjectEl = document.getElementById('current-subject');
    const currentTasksEl = document.getElementById('current-tasks');
    const nextSubjectEl = document.getElementById('next-subject');
    const nextTimeEl = document.getElementById('next-time');
    const progressFillEl = document.getElementById('progress-fill');
    const progressTextEl = document.getElementById('progress-text');

    // 統計功能相關的 DOM 元素
    const totalClassesEl = document.getElementById('total-classes');
    const completedClassesEl = document.getElementById('completed-classes');
    const remainingClassesEl = document.getElementById('remaining-classes');
    const currentProgressEl = document.getElementById('current-progress');

    // 倒數計時器相關的 DOM 元素
    const countdownTimerEl = document.getElementById('countdown-timer');
    const countdownSettingsBtn = document.getElementById('countdown-settings-btn');
    const countdownSettingsModal = document.getElementById('countdown-settings-modal');
    const closeCountdownSettings = document.getElementById('close-countdown-settings');
    const countdownSettingsForm = document.getElementById('countdown-settings-form');
    const countdownMinutesInput = document.getElementById('countdown-minutes');
    const cancelCountdownSettings = document.getElementById('cancel-countdown-settings');

    // 匯出功能相關的 DOM 元素
    const exportScheduleBtn = document.getElementById('export-schedule-btn');
    const exportModal = document.getElementById('export-modal');
    const closeExportModal = document.getElementById('close-export-modal');
    const exportFormatBtns = document.querySelectorAll('.export-format-btn');
    const includeWeekendCheckbox = document.getElementById('include-weekend');
    const includeTasksCheckbox = document.getElementById('include-tasks');
    const includeSpecialTimesCheckbox = document.getElementById('include-special-times');
    const exportFilenameInput = document.getElementById('export-filename');

    // 匯入功能相關的 DOM 元素
    const importFileInput = document.getElementById('import-file-input');
    const importFileBtn = document.getElementById('import-file-btn');
    const resetScheduleBtn = document.getElementById('reset-schedule-btn');
    const exportJsBtn = document.getElementById('export-js-btn');

    // 音樂設定相關的 DOM 元素
    const musicSettingsBtn = document.getElementById('music-settings-btn');
    const testMusicBtn = document.getElementById('test-music-btn');
    const stopMusicBtn = document.getElementById('stop-music-btn');
    const musicSettingsModal = document.getElementById('music-settings-modal');
    const closeMusicSettings = document.getElementById('close-music-settings');
    const musicSettingsForm = document.getElementById('music-settings-form');
    const cancelMusicSettings = document.getElementById('cancel-music-settings');
    const stopTestMusicBtn = document.getElementById('stop-test-music');
    const enableMusicCheckbox = document.getElementById('enable-music');
    const musicVolumeSlider = document.getElementById('music-volume');
    const volumeDisplay = document.getElementById('volume-display');
    const classStartSoundSelect = document.getElementById('class-start-sound');
    const countdownEndSoundSelect = document.getElementById('countdown-end-sound');
    const classStartFileInput = document.getElementById('class-start-file');
    const countdownEndFileInput = document.getElementById('countdown-end-file');
    const musicDurationInput = document.getElementById('music-duration');

    // 中午和午休音樂相關元素
    const lunchMusicSelect = document.getElementById('lunch-music');
    const napMusicSelect = document.getElementById('nap-music');
    const lunchMusicFileInput = document.getElementById('lunch-music-file');
    const napMusicFileInput = document.getElementById('nap-music-file');
    const lunchTimeStartInput = document.getElementById('lunch-time-start');
    const lunchTimeEndInput = document.getElementById('lunch-time-end');
    const napTimeStartInput = document.getElementById('nap-time-start');
    const napTimeEndInput = document.getElementById('nap-time-end');
    const testLunchMusicBtn = document.getElementById('test-lunch-music-btn');
    const testNapMusicBtn = document.getElementById('test-nap-music-btn');
    const dismissalMusicSelect = document.getElementById('dismissal-music');
    const dismissalMusicFileInput = document.getElementById('dismissal-music-file');
    const dismissalTimeStartInput = document.getElementById('dismissal-time-start');
    const dismissalTimeEndInput = document.getElementById('dismissal-time-end');
    const testDismissalMusicBtn = document.getElementById('test-dismissal-music-btn');
    const cleaningMusicSelect = document.getElementById('cleaning-music');
    const cleaningMusicFileInput = document.getElementById('cleaning-music-file');
    const cleaningTimeStartInput = document.getElementById('cleaning-time-start');
    const cleaningTimeEndInput = document.getElementById('cleaning-time-end');
    const testCleaningMusicBtn = document.getElementById('test-cleaning-music-btn');
    const lunchPrepMusicSelect = document.getElementById('lunch-prep-music');
    const lunchPrepMusicFileInput = document.getElementById('lunch-prep-music-file');
    const lunchPrepTimeStartInput = document.getElementById('lunch-prep-time-start');
    const lunchPrepTimeEndInput = document.getElementById('lunch-prep-time-end');
    const testLunchPrepMusicBtn = document.getElementById('test-lunch-prep-music-btn');

    // 休息時間編輯相關的 DOM 元素
    const breakTimeModal = document.getElementById('break-time-modal');
    const closeBreakTimeModal = document.getElementById('close-break-time-modal');
    const breakTimeForm = document.getElementById('break-time-form');
    const breakTimeTitle = document.getElementById('break-time-title');
    const breakStartTimeInput = document.getElementById('break-start-time');
    const breakEndTimeInput = document.getElementById('break-end-time');
    const breakDescriptionInput = document.getElementById('break-description');
    const cancelBreakTime = document.getElementById('cancel-break-time');

    // 測試時間相關的 DOM 元素
    const timeTestBtn = document.getElementById('time-test-btn');
    const resetTimeBtn = document.getElementById('reset-time-btn');
    const timeTestModal = document.getElementById('time-test-modal');
    const closeTimeTest = document.getElementById('close-time-test');
    const testTimeInput = document.getElementById('test-time-input');
    const testDateInput = document.getElementById('test-date-input');
    const cancelTimeTest = document.getElementById('cancel-time-test');
    const applyTestTime = document.getElementById('apply-test-time');

    let countdownInterval = null;
    let countdownMinutes = 2; // 預設2分鐘
    let isCountdownRunning = false; // 追蹤倒數計時器狀態
    let currentCountdownClassId = null; // 追蹤當前倒數計時的課程ID

    // 測試時間相關變數
    let isTestMode = false; // 是否在測試模式
    let testTime = null; // 測試時間
    let testDate = null; // 測試日期

    // 測試音樂播放相關變數
    let currentTestAudio = null; // 當前播放的測試音樂
    let testMusicTimeout = null; // 測試音樂的定時器

    /**
     * 停止當前測試音樂
     */
    function stopTestMusic() {
        if (currentTestAudio) {
            currentTestAudio.pause();
            currentTestAudio.currentTime = 0;
            currentTestAudio = null;
        }
        if (testMusicTimeout) {
            clearTimeout(testMusicTimeout);
            testMusicTimeout = null;
        }
        // 也停止其他可能的音樂播放
        if (typeof stopCurrentMusic === 'function') {
            stopCurrentMusic();
        }
    }

    // 音樂播放相關變數
    let audioContext = null;
    let currentAudioSource = null; // 當前播放的音頻源
    let musicPlaybackTimeout = null; // 音樂播放計時器
    let musicSettings = {
        enabled: true,
        volume: 0.5,
        classStartSound: 'none',
        countdownEndSound: 'none',
        duration: 3,
        lunchMusic: 'none',
        napMusic: 'none',
        lunchTimeStart: '11:50',
        lunchTimeEnd: '12:35',
        napTimeStart: '12:35',
        napTimeEnd: '13:15',
        dismissalMusic: 'none',
        dismissalTimeStart: '15:45',
        dismissalTimeEnd: '16:00',
        cleaningMusic: 'none',
        cleaningTimeStart: '07:50',
        cleaningTimeEnd: '08:05',
        lunchPrepMusic: 'none',
        lunchPrepTimeStart: '12:20',
        lunchPrepTimeEnd: '12:30',
        customSounds: {
            classStart: null,
            countdownEnd: null,
            lunch: null,
            nap: null,
            dismissal: null,
            cleaning: null,
            lunchPrep: null
        }
    };

    // 編輯功能相關的 DOM 元素
    const editScheduleBtn = document.getElementById('edit-schedule-btn');
    const clearDataBtn = document.getElementById('clear-data-btn');
    const editModal = document.getElementById('edit-modal');
    const closeModal = document.getElementById('close-modal');
    const addClassBtn = document.getElementById('add-class-btn');
    const saveScheduleBtn = document.getElementById('save-schedule-btn');
    const scheduleList = document.getElementById('schedule-list');
    
    // 課程表單相關的 DOM 元素
    const classFormModal = document.getElementById('class-form-modal');
    const closeFormModal = document.getElementById('close-form-modal');
    const classForm = document.getElementById('class-form');
    const formTitle = document.getElementById('form-title');
    const cancelForm = document.getElementById('cancel-form');

    // 字體大小控制相關的 DOM 元素
    const fontSizeBtns = document.querySelectorAll('.font-size-btn');
    const themeToggleBtn = document.getElementById('theme-toggle-btn');

    let schedule = [];
    let notifiedClassId = null; // Keep track of notified classes to avoid spam
    let editingClassId = null; // Track which class is being edited

    // 科目顏色配置
    const subjectColors = {
        '國語': {
            primary: '#e74c3c',
            secondary: '#c0392b',
            background: '#fdf2f2'
        },
        '數學': {
            primary: '#3498db',
            secondary: '#2980b9',
            background: '#f0f8ff'
        },
        '英語': {
            primary: '#9b59b6',
            secondary: '#8e44ad',
            background: '#f8f4ff'
        },
        '自然科學': {
            primary: '#27ae60',
            secondary: '#229954',
            background: '#f0fff4'
        },
        '社會': {
            primary: '#f39c12',
            secondary: '#e67e22',
            background: '#fff8f0'
        },
        '健康與體育': {
            primary: '#1abc9c',
            secondary: '#16a085',
            background: '#f0fffd'
        },
        '藝術與人文': {
            primary: '#e67e22',
            secondary: '#d35400',
            background: '#fff5f0'
        },
        '綜合活動': {
            primary: '#34495e',
            secondary: '#2c3e50',
            background: '#f8f9fa'
        }
    };

    // 字體大小設定
    const fontSizeSettings = {
        tiny: {
            '--header-h1-size': '2.5em',
            '--time-size': '2.0em',
            '--card-h2-size': '1.8em',
            '--subject-size': '2.8em',
            '--card-h3-size': '1.6em',
            '--card-li-size': '1.3em',
            '--next-class-p-size': '1.5em',
            '--no-class-h2-size': '2.3em',
            '--no-class-p-size': '1.4em',
            '--edit-button-size': '1.2em',
            '--modal-h2-size': '2.1em',
            '--form-label-size': '1.4em',
            '--form-input-size': '1.4em',
            '--schedule-item-title-size': '1.5em',
            '--schedule-item-details-size': '1.25em'
        },
        small: {
            '--header-h1-size': '3.5em',
            '--time-size': '3.0em',
            '--card-h2-size': '2.7em',
            '--subject-size': '4.2em',
            '--card-h3-size': '2.4em',
            '--card-li-size': '1.95em',
            '--next-class-p-size': '2.25em',
            '--no-class-h2-size': '3.45em',
            '--no-class-p-size': '2.1em',
            '--edit-button-size': '1.8em',
            '--modal-h2-size': '3.15em',
            '--form-label-size': '2.1em',
            '--form-input-size': '2.1em',
            '--schedule-item-title-size': '2.25em',
            '--schedule-item-details-size': '1.875em'
        },
        medium: {
            '--header-h1-size': '7.0em',
            '--time-size': '6.0em',
            '--card-h2-size': '5.0em',
            '--subject-size': '7.6em',
            '--card-h3-size': '4.0em',
            '--card-li-size': '3.2em',
            '--next-class-p-size': '3.6em',
            '--no-class-h2-size': '5.6em',
            '--no-class-p-size': '3.2em',
            '--edit-button-size': '2.8em',
            '--modal-h2-size': '5.0em',
            '--form-label-size': '3.2em',
            '--form-input-size': '3.2em',
            '--schedule-item-title-size': '3.5em',
            '--schedule-item-details-size': '3.0em'
        },
        large: {
            '--header-h1-size': '9.0em',
            '--time-size': '8.0em',
            '--card-h2-size': '6.4em',
            '--subject-size': '10.0em',
            '--card-h3-size': '5.0em',
            '--card-li-size': '4.0em',
            '--next-class-p-size': '4.4em',
            '--no-class-h2-size': '7.0em',
            '--no-class-p-size': '4.0em',
            '--edit-button-size': '3.6em',
            '--modal-h2-size': '6.0em',
            '--form-label-size': '4.0em',
            '--form-input-size': '4.0em',
            '--schedule-item-title-size': '4.5em',
            '--schedule-item-details-size': '3.8em'
        },
        xlarge: {
            '--header-h1-size': '11.0em',
            '--time-size': '10.0em',
            '--card-h2-size': '8.0em',
            '--subject-size': '12.0em',
            '--card-h3-size': '6.0em',
            '--card-li-size': '5.0em',
            '--next-class-p-size': '5.6em',
            '--no-class-h2-size': '8.4em',
            '--no-class-p-size': '5.0em',
            '--edit-button-size': '4.4em',
            '--modal-h2-size': '7.0em',
            '--form-label-size': '5.0em',
            '--form-input-size': '5.0em',
            '--schedule-item-title-size': '5.5em',
            '--schedule-item-details-size': '4.5em'
        }
    };

    // --- 音樂播放功能 ---

    /**
     * 初始化音頻上下文
     */
    function initAudioContext() {
        if (!audioContext) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        return audioContext;
    }

    /**
     * 生成預設音效
     * @param {string} type - 音效類型 ('bell', 'chime', 'beep', 'alarm', 'ding', 'whistle')
     * @param {number} duration - 音效持續時間（秒）
     * @returns {AudioBuffer} 音效緩衝區
     */
    async function generateSound(type, duration = 1) {
        const ctx = initAudioContext();
        const sampleRate = ctx.sampleRate;
        const length = sampleRate * duration;
        const buffer = ctx.createBuffer(1, length, sampleRate);
        const data = buffer.getChannelData(0);

        switch (type) {
            case 'bell':
                // 經典鈴聲 - 多個頻率的正弦波
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = Math.exp(-t * 2); // 衰減包絡
                    data[i] = envelope * (
                        Math.sin(2 * Math.PI * 523.25 * t) * 0.3 + // C5
                        Math.sin(2 * Math.PI * 659.25 * t) * 0.2 + // E5
                        Math.sin(2 * Math.PI * 783.99 * t) * 0.1   // G5
                    );
                }
                break;

            case 'chime':
                // 輕柔鐘聲
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = Math.exp(-t * 1.5);
                    data[i] = envelope * Math.sin(2 * Math.PI * 440 * t) * 0.5;
                }
                break;

            case 'beep':
                // 電子提示音
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = t < 0.1 ? t * 10 : (t > duration - 0.1 ? (duration - t) * 10 : 1);
                    data[i] = envelope * Math.sin(2 * Math.PI * 800 * t) * 0.3;
                }
                break;

            case 'alarm':
                // 警報聲
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const freq = 400 + Math.sin(2 * Math.PI * 2 * t) * 200; // 頻率調制
                    data[i] = Math.sin(2 * Math.PI * freq * t) * 0.4;
                }
                break;

            case 'ding':
                // 叮咚聲
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = Math.exp(-t * 3);
                    data[i] = envelope * (
                        Math.sin(2 * Math.PI * 1000 * t) * 0.5 +
                        Math.sin(2 * Math.PI * 1500 * t) * 0.3
                    );
                }
                break;

            case 'whistle':
                // 哨聲
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = t < 0.1 ? t * 10 : (t > duration - 0.1 ? (duration - t) * 10 : 1);
                    data[i] = envelope * Math.sin(2 * Math.PI * 2000 * t) * 0.3;
                }
                break;

            case 'relaxing':
                // 輕鬆音樂 - 柔和的和弦
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = Math.sin(Math.PI * t / duration); // 平滑包絡
                    data[i] = envelope * (
                        Math.sin(2 * Math.PI * 261.63 * t) * 0.2 + // C4
                        Math.sin(2 * Math.PI * 329.63 * t) * 0.15 + // E4
                        Math.sin(2 * Math.PI * 392.00 * t) * 0.1    // G4
                    );
                }
                break;

            case 'classical':
                // 古典音樂風格 - 簡單的旋律
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = Math.sin(Math.PI * t / duration);
                    const melody = Math.sin(2 * Math.PI * (440 + Math.sin(t * 2) * 50) * t);
                    data[i] = envelope * melody * 0.2;
                }
                break;

            case 'nature':
                // 自然音效 - 模擬鳥鳴和風聲
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = 0.5 + 0.3 * Math.sin(t * 0.5);
                    const bird = Math.sin(2 * Math.PI * (800 + Math.random() * 400) * t) * 0.1;
                    const wind = (Math.random() - 0.5) * 0.05;
                    data[i] = envelope * (bird + wind);
                }
                break;

            case 'lullaby':
                // 搖籃曲 - 溫柔的旋律
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = Math.sin(Math.PI * t / duration);
                    data[i] = envelope * (
                        Math.sin(2 * Math.PI * 220 * t) * 0.15 + // A3
                        Math.sin(2 * Math.PI * 277.18 * t) * 0.1 + // C#4
                        Math.sin(2 * Math.PI * 329.63 * t) * 0.08   // E4
                    );
                }
                break;

            case 'ambient':
                // 環境音樂 - 深沉的氛圍音
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    const envelope = Math.sin(Math.PI * t / duration);
                    data[i] = envelope * (
                        Math.sin(2 * Math.PI * 55 * t) * 0.1 + // A1
                        Math.sin(2 * Math.PI * 82.41 * t) * 0.08 + // E2
                        Math.sin(2 * Math.PI * 110 * t) * 0.06     // A2
                    );
                }
                break;

            case 'white-noise':
                // 白噪音
                for (let i = 0; i < length; i++) {
                    const envelope = 0.3 + 0.2 * Math.sin(i / sampleRate * 0.5);
                    data[i] = envelope * (Math.random() - 0.5) * 0.1;
                }
                break;

            default:
                // 預設音效
                for (let i = 0; i < length; i++) {
                    const t = i / sampleRate;
                    data[i] = Math.sin(2 * Math.PI * 440 * t) * 0.3;
                }
        }

        return buffer;
    }

    /**
     * 停止當前播放的音樂
     */
    function stopCurrentMusic() {
        if (currentAudioSource) {
            try {
                currentAudioSource.stop();
            } catch (e) {
                // 音效可能已經結束
            }
            currentAudioSource = null;
        }

        if (musicPlaybackTimeout) {
            clearTimeout(musicPlaybackTimeout);
            musicPlaybackTimeout = null;
        }

        if (stopMusicBtn) {
            stopMusicBtn.style.display = 'none';
        }
    }

    /**
     * 播放音效
     * @param {string} soundType - 音效類型
     * @param {number} volume - 音量 (0-1)
     * @param {number} duration - 持續時間（秒）
     */
    async function playSound(soundType, volume = 0.5, duration = 1) {
        if (!musicSettings.enabled) return;

        // 檢查是否設定為不播放
        if (soundType === 'classStart' && musicSettings.classStartSound === 'none') return;
        if (soundType === 'countdownEnd' && musicSettings.countdownEndSound === 'none') return;

        try {
            const ctx = initAudioContext();
            
            // 如果音頻上下文被暫停，嘗試恢復
            if (ctx.state === 'suspended') {
                await ctx.resume();
            }

            let buffer;
            
            // 檢查是否有自訂音效
            if (soundType === 'classStart' && musicSettings.customSounds.classStart) {
                buffer = musicSettings.customSounds.classStart.audioBuffer || musicSettings.customSounds.classStart;
            } else if (soundType === 'countdownEnd' && musicSettings.customSounds.countdownEnd) {
                buffer = musicSettings.customSounds.countdownEnd.audioBuffer || musicSettings.customSounds.countdownEnd;
            } else if (soundType === 'lunch' && musicSettings.customSounds.lunch) {
                // 中午音樂使用自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.lunch;
                audio.volume = volume * musicSettings.volume;
                audio.play();
                return;
            } else if (soundType === 'nap' && musicSettings.customSounds.nap) {
                // 午休音樂使用自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.nap;
                audio.volume = volume * musicSettings.volume;
                audio.play();
                return;
            } else if (soundType === 'dismissal' && musicSettings.customSounds.dismissal) {
                // 放學準備音樂使用自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.dismissal;
                audio.volume = volume * musicSettings.volume;
                audio.play();
                return;
            } else if (soundType === 'cleaning' && musicSettings.customSounds.cleaning) {
                // 掃地音樂使用自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.cleaning;
                audio.volume = volume * musicSettings.volume;
                audio.play();
                return;
            } else if (soundType === 'lunchPrep' && musicSettings.customSounds.lunchPrep) {
                // 午餐工作音樂使用自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.lunchPrep;
                audio.volume = volume * musicSettings.volume;
                audio.play();
                return;
            } else {
                // 使用預設音效
                let defaultType;
                if (soundType === 'classStart') {
                    defaultType = musicSettings.classStartSound;
                } else if (soundType === 'countdownEnd') {
                    defaultType = musicSettings.countdownEndSound;
                } else if (soundType === 'lunch') {
                    defaultType = musicSettings.lunchMusic;
                } else if (soundType === 'nap') {
                    defaultType = musicSettings.napMusic;
                } else if (soundType === 'dismissal') {
                    defaultType = musicSettings.dismissalMusic;
                } else if (soundType === 'cleaning') {
                    defaultType = musicSettings.cleaningMusic;
                } else if (soundType === 'lunchPrep') {
                    defaultType = musicSettings.lunchPrepMusic;
                } else {
                    defaultType = soundType;
                }
                buffer = await generateSound(defaultType, duration);
            }

            // 停止之前的音樂播放
            stopCurrentMusic();

            const source = ctx.createBufferSource();
            const gainNode = ctx.createGain();

            source.buffer = buffer;
            gainNode.gain.value = volume * musicSettings.volume;

            source.connect(gainNode);
            gainNode.connect(ctx.destination);

            // 儲存當前播放的音頻源
            currentAudioSource = source;

            source.start();

            // 顯示停止按鈕
            if (stopMusicBtn) {
                stopMusicBtn.style.display = 'inline-block';
            }

            // 設定音效結束時間
            musicPlaybackTimeout = setTimeout(() => {
                try {
                    source.stop();
                    currentAudioSource = null;
                    if (stopMusicBtn) {
                        stopMusicBtn.style.display = 'none';
                    }
                } catch (e) {
                    // 音效可能已經結束
                }
            }, duration * 1000);

            // 當音效自然結束時也要清理
            source.onended = () => {
                currentAudioSource = null;
                if (stopMusicBtn) {
                    stopMusicBtn.style.display = 'none';
                }
                if (musicPlaybackTimeout) {
                    clearTimeout(musicPlaybackTimeout);
                    musicPlaybackTimeout = null;
                }
            };

        } catch (error) {
            console.error('播放音效時發生錯誤:', error);
        }
    }

    /**
     * 載入自訂音效檔案
     * @param {File} file - 音效檔案
     * @returns {Promise<Object>} 包含 AudioBuffer 和原始資料的物件
     */
    async function loadCustomSound(file) {
        return new Promise((resolve, reject) => {
            // 先讀取為 DataURL 以便儲存
            const dataUrlReader = new FileReader();
            dataUrlReader.onload = (e) => {
                const audioData = e.target.result;

                // 再讀取為 ArrayBuffer 以便播放
                const arrayBufferReader = new FileReader();
                arrayBufferReader.onload = async (e) => {
                    try {
                        const ctx = initAudioContext();
                        const arrayBuffer = e.target.result;
                        const audioBuffer = await ctx.decodeAudioData(arrayBuffer);

                        // 返回包含 AudioBuffer 和原始資料的物件
                        resolve({
                            audioBuffer: audioBuffer,
                            audioData: audioData
                        });
                    } catch (error) {
                        reject(error);
                    }
                };
                arrayBufferReader.onerror = reject;
                arrayBufferReader.readAsArrayBuffer(file);
            };
            dataUrlReader.onerror = reject;
            dataUrlReader.readAsDataURL(file);
        });
    }

    /**
     * 恢復自訂音樂設定
     * @param {Object} customSounds - 儲存的自訂音樂資料
     */
    async function restoreCustomSounds(customSounds) {
        try {
            // 恢復上課鈴聲
            if (customSounds.classStart) {
                const audioBuffer = await dataUrlToAudioBuffer(customSounds.classStart);
                musicSettings.customSounds.classStart = {
                    audioBuffer: audioBuffer,
                    audioData: customSounds.classStart
                };
            }

            // 恢復倒數結束音效
            if (customSounds.countdownEnd) {
                const audioBuffer = await dataUrlToAudioBuffer(customSounds.countdownEnd);
                musicSettings.customSounds.countdownEnd = {
                    audioBuffer: audioBuffer,
                    audioData: customSounds.countdownEnd
                };
            }

            // 恢復其他音樂（已經是 DataURL 格式）
            if (customSounds.lunch) {
                musicSettings.customSounds.lunch = customSounds.lunch;
            }
            if (customSounds.nap) {
                musicSettings.customSounds.nap = customSounds.nap;
            }
            if (customSounds.dismissal) {
                musicSettings.customSounds.dismissal = customSounds.dismissal;
            }
            if (customSounds.cleaning) {
                musicSettings.customSounds.cleaning = customSounds.cleaning;
            }
            if (customSounds.lunchPrep) {
                musicSettings.customSounds.lunchPrep = customSounds.lunchPrep;
            }

            console.log('自訂音樂已恢復');
        } catch (error) {
            console.error('恢復自訂音樂失敗:', error);
        }
    }

    /**
     * 將 DataURL 轉換為 AudioBuffer
     * @param {string} dataUrl - DataURL 格式的音樂資料
     * @returns {Promise<AudioBuffer>} AudioBuffer 物件
     */
    async function dataUrlToAudioBuffer(dataUrl) {
        return new Promise((resolve, reject) => {
            try {
                // 將 DataURL 轉換為 ArrayBuffer
                const byteString = atob(dataUrl.split(',')[1]);
                const arrayBuffer = new ArrayBuffer(byteString.length);
                const uint8Array = new Uint8Array(arrayBuffer);

                for (let i = 0; i < byteString.length; i++) {
                    uint8Array[i] = byteString.charCodeAt(i);
                }

                // 解碼為 AudioBuffer
                const ctx = initAudioContext();
                ctx.decodeAudioData(arrayBuffer)
                    .then(resolve)
                    .catch(reject);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 載入音樂設定
     */
    async function loadMusicSettings() {
        if (isElectron) {
            // Electron 環境：從檔案載入設定
            try {
                const { ipcRenderer } = window.require('electron');
                const result = await ipcRenderer.invoke('load-settings');

                if (result.success && result.data && result.data.musicSettings) {
                    musicSettings = { ...musicSettings, ...result.data.musicSettings };

                    // 恢復自訂音樂
                    if (result.data.musicSettings.customSounds) {
                        await restoreCustomSounds(result.data.musicSettings.customSounds);
                    }

                    console.log('已從檔案載入音樂設定');
                } else {
                    console.log('使用預設音樂設定');
                }
            } catch (error) {
                console.error('從檔案載入音樂設定失敗:', error);
                // 降級到 localStorage
                await loadMusicSettingsFromLocalStorage();
            }
        } else {
            // 瀏覽器環境：使用 localStorage
            await loadMusicSettingsFromLocalStorage();
        }
        updateMusicSettingsUI();
    }

    /**
     * 從 localStorage 載入音樂設定（降級方案）
     */
    async function loadMusicSettingsFromLocalStorage() {
        const saved = localStorage.getItem('musicSettings');
        if (saved) {
            try {
                const parsed = JSON.parse(saved);
                musicSettings = { ...musicSettings, ...parsed };

                // 恢復自訂音樂
                if (parsed.customSounds) {
                    await restoreCustomSounds(parsed.customSounds);
                }
            } catch (error) {
                console.error('載入音樂設定失敗:', error);
            }
        }
    }

    /**
     * 儲存音樂設定
     */
    async function saveMusicSettings() {
        try {
            // 準備可儲存的自訂音樂資料
            const customSoundsToSave = {};

            // 將 AudioBuffer 轉換為可儲存的格式（對於上課鈴聲和倒數結束音效）
            if (musicSettings.customSounds.classStart) {
                if (musicSettings.customSounds.classStart.audioData) {
                    customSoundsToSave.classStart = musicSettings.customSounds.classStart.audioData;
                } else if (typeof musicSettings.customSounds.classStart === 'string') {
                    // 向後相容：如果是舊格式的 DataURL
                    customSoundsToSave.classStart = musicSettings.customSounds.classStart;
                }
            }
            if (musicSettings.customSounds.countdownEnd) {
                if (musicSettings.customSounds.countdownEnd.audioData) {
                    customSoundsToSave.countdownEnd = musicSettings.customSounds.countdownEnd.audioData;
                } else if (typeof musicSettings.customSounds.countdownEnd === 'string') {
                    // 向後相容：如果是舊格式的 DataURL
                    customSoundsToSave.countdownEnd = musicSettings.customSounds.countdownEnd;
                }
            }

            // 其他音樂已經是 DataURL 格式，可以直接儲存
            if (musicSettings.customSounds.lunch) {
                customSoundsToSave.lunch = musicSettings.customSounds.lunch;
            }
            if (musicSettings.customSounds.nap) {
                customSoundsToSave.nap = musicSettings.customSounds.nap;
            }
            if (musicSettings.customSounds.dismissal) {
                customSoundsToSave.dismissal = musicSettings.customSounds.dismissal;
            }
            if (musicSettings.customSounds.cleaning) {
                customSoundsToSave.cleaning = musicSettings.customSounds.cleaning;
            }
            if (musicSettings.customSounds.lunchPrep) {
                customSoundsToSave.lunchPrep = musicSettings.customSounds.lunchPrep;
            }

            const settingsToSave = {
                enabled: musicSettings.enabled,
                volume: musicSettings.volume,
                classStartSound: musicSettings.classStartSound,
                countdownEndSound: musicSettings.countdownEndSound,
                duration: musicSettings.duration,
                lunchMusic: musicSettings.lunchMusic,
                napMusic: musicSettings.napMusic,
                dismissalMusic: musicSettings.dismissalMusic,
                cleaningMusic: musicSettings.cleaningMusic,
                lunchPrepMusic: musicSettings.lunchPrepMusic,
                lunchTimeStart: musicSettings.lunchTimeStart,
                lunchTimeEnd: musicSettings.lunchTimeEnd,
                napTimeStart: musicSettings.napTimeStart,
                napTimeEnd: musicSettings.napTimeEnd,
                dismissalTimeStart: musicSettings.dismissalTimeStart,
                dismissalTimeEnd: musicSettings.dismissalTimeEnd,
                cleaningTimeStart: musicSettings.cleaningTimeStart,
                cleaningTimeEnd: musicSettings.cleaningTimeEnd,
                lunchPrepTimeStart: musicSettings.lunchPrepTimeStart,
                lunchPrepTimeEnd: musicSettings.lunchPrepTimeEnd,
                customSounds: customSoundsToSave
            };

            if (isElectron) {
                // Electron 環境：儲存到檔案
                try {
                    const { ipcRenderer } = window.require('electron');
                    
                    // 載入現有設定
                    const currentResult = await ipcRenderer.invoke('load-settings');
                    let allSettings = {};
                    
                    if (currentResult.success && currentResult.data) {
                        allSettings = currentResult.data;
                    }
                    
                    // 更新音樂設定部分
                    allSettings.musicSettings = settingsToSave;
                    
                    // 儲存完整設定
                    const result = await ipcRenderer.invoke('save-settings', allSettings);
                    
                    if (result.success) {
                        console.log('音樂設定已儲存到檔案');
                    } else {
                        throw new Error(result.error);
                    }
                } catch (error) {
                    console.error('儲存音樂設定到檔案失敗，降級到 localStorage:', error);
                    // 降級到 localStorage
                    localStorage.setItem('musicSettings', JSON.stringify(settingsToSave));
                }
            } else {
                // 瀏覽器環境：使用 localStorage
                localStorage.setItem('musicSettings', JSON.stringify(settingsToSave));
            }
        } catch (error) {
            console.error('儲存音樂設定失敗:', error);
        }
    }

    /**
     * 更新音樂設定介面
     */
    function updateMusicSettingsUI() {
        if (enableMusicCheckbox) enableMusicCheckbox.checked = musicSettings.enabled;
        if (musicVolumeSlider) musicVolumeSlider.value = musicSettings.volume * 100;
        if (volumeDisplay) volumeDisplay.textContent = Math.round(musicSettings.volume * 100) + '%';
        if (classStartSoundSelect) classStartSoundSelect.value = musicSettings.classStartSound;
        if (countdownEndSoundSelect) countdownEndSoundSelect.value = musicSettings.countdownEndSound;
        if (musicDurationInput) musicDurationInput.value = musicSettings.duration;

        // 更新所有特殊時段音樂設定
        if (lunchMusicSelect) lunchMusicSelect.value = musicSettings.lunchMusic;
        if (napMusicSelect) napMusicSelect.value = musicSettings.napMusic;
        if (dismissalMusicSelect) dismissalMusicSelect.value = musicSettings.dismissalMusic;
        if (cleaningMusicSelect) cleaningMusicSelect.value = musicSettings.cleaningMusic;
        if (lunchPrepMusicSelect) lunchPrepMusicSelect.value = musicSettings.lunchPrepMusic;
        if (lunchTimeStartInput) lunchTimeStartInput.value = musicSettings.lunchTimeStart;
        if (lunchTimeEndInput) lunchTimeEndInput.value = musicSettings.lunchTimeEnd;
        if (napTimeStartInput) napTimeStartInput.value = musicSettings.napTimeStart;
        if (napTimeEndInput) napTimeEndInput.value = musicSettings.napTimeEnd;
        if (dismissalTimeStartInput) dismissalTimeStartInput.value = musicSettings.dismissalTimeStart;
        if (dismissalTimeEndInput) dismissalTimeEndInput.value = musicSettings.dismissalTimeEnd;
        if (cleaningTimeStartInput) cleaningTimeStartInput.value = musicSettings.cleaningTimeStart;
        if (cleaningTimeEndInput) cleaningTimeEndInput.value = musicSettings.cleaningTimeEnd;
        if (lunchPrepTimeStartInput) lunchPrepTimeStartInput.value = musicSettings.lunchPrepTimeStart;
        if (lunchPrepTimeEndInput) lunchPrepTimeEndInput.value = musicSettings.lunchPrepTimeEnd;
    }

    // --- Main Functions ---

    /**
     * 取得內嵌的課程表資料（作為 CORS 問題的後備方案）
     */
    function getEmbeddedSchedule() {
        // 使用從 schedule-data.js 載入的全域變數
        if (window.EMBEDDED_SCHEDULE) {
            return window.EMBEDDED_SCHEDULE;
        }

        // 如果全域變數不存在，返回基本的週一課程作為最後的後備方案
        console.warn('EMBEDDED_SCHEDULE not found, using minimal fallback data');
        return [
            {
                "id": 1,
                "day": "Monday",
                "start": "08:05",
                "end": "08:25",
                "subject": "導師時間",
                "tasks": [
                    "準時到校",
                    "準備晨會",
                    "整理書包和課本"
                ]
            },
            {
                "id": 2,
                "day": "Monday",
                "start": "08:30",
                "end": "09:10",
                "subject": "國語",
                "tasks": [
                    "拿出國語課本和鉛筆盒",
                    "打開課本到指定課文",
                    "準備好筆記本和字典"
                ]
            }
        ];
    }

    /**
     * Fetches the schedule from the JSON file.
     */
    async function loadSchedule() {
        try {
            // 在 Electron 中優先以 fs 直接讀檔，避免在 file:// 協定下 fetch 的限制
            if (isElectron && window.__ELECTRON_FS__ && window.__ELECTRON_PATH__) {
                const fs = window.__ELECTRON_FS__;
                const path = window.__ELECTRON_PATH__;
                const schedulePath = path.join(__dirname, 'schedule.json');
                const raw = fs.readFileSync(schedulePath, 'utf-8');
                const scheduleData = JSON.parse(raw);
                updateSchedule(scheduleData);
                return;
            }

            // 瀏覽器環境：先嘗試 fetch，失敗則使用內嵌資料
            try {
                const response = await fetch('schedule.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const scheduleData = await response.json();
                updateSchedule(scheduleData);
                return;
            } catch (fetchError) {
                console.warn('Fetch failed, using embedded schedule data:', fetchError);
                // 使用內嵌的課程表資料作為後備方案
                updateSchedule(getEmbeddedSchedule());
            }
        } catch (error) {
            console.error('Could not load schedule:', error);
            alert('無法載入課程表！請確認 schedule.json 檔案是否存在且格式正確。');
        }
    }

    /**
     * Updates the global schedule variable and sorts it.
     * @param {Array} newSchedule - The new schedule array.
     */
    function updateSchedule(newSchedule) {
        schedule = newSchedule;
        schedule.sort((a, b) => a.start.localeCompare(b.start));
        // Force an immediate check after updating
        checkSchedule(); 
    }

    /**
     * Updates the clock every second to show date, day of week, and time.
     */
    function updateTime() {
        let now;

        if (isTestMode && testTime) {
            // 測試模式：使用設定的測試時間
            now = new Date();
            if (testDate) {
                const [year, month, day] = testDate.split('-');
                now = new Date(year, month - 1, day);
            }
            const [hours, minutes] = testTime.split(':');
            now.setHours(parseInt(hours), parseInt(minutes), 0, 0);
        } else {
            // 正常模式：使用當前時間
            now = new Date();
        }

        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        };

        let timeText = now.toLocaleDateString('zh-TW', options);
        if (isTestMode) {
            timeText += ' (測試模式)';
        }

        currentTimeEl.textContent = timeText;
    }

    /**
     * Finds the current and next class based on the current time and day.
     */
    function checkSchedule() {
        let now;

        if (isTestMode && testTime) {
            // 測試模式：使用設定的測試時間
            now = new Date();
            if (testDate) {
                const [year, month, day] = testDate.split('-');
                now = new Date(year, month - 1, day);
            }
            const [hours, minutes] = testTime.split(':');
            now.setHours(parseInt(hours), parseInt(minutes), 0, 0);
        } else {
            // 正常模式：使用當前時間
            now = new Date();
        }

        const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
        const currentDay = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][now.getDay()];

        // 只檢查今天的課程
        const todaySchedule = schedule.filter(c => c.day === currentDay);

        const currentClass = todaySchedule.find(c => currentTime >= c.start && currentTime < c.end);
        // Find the first class that starts after the *current* time today
        const nextClass = todaySchedule.find(c => c.start > currentTime);

        updateUI(currentClass, nextClass, currentDay);

        if (currentClass && notifiedClassId !== currentClass.id) {
            sendNotification(currentClass);
            notifiedClassId = currentClass.id;
            
            // 上課時頁面跳到最前面
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        // Reset notification tracking when there's no class
        if (!currentClass) {
            notifiedClassId = null;
        }
    }

    /**
     * 啟動倒數計時器
     */
    function startCountdown() {
        // 如果倒數計時器已經在運行，不要重新啟動
        if (isCountdownRunning) {
            return;
        }

        // 清除之前的計時器
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }

        // 重置倒數計時器顯示
        const minutes = countdownMinutes;
        const seconds = 0;
        countdownTimerEl.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

        // 設置倒數時間（轉換為秒）
        let timeLeft = minutes * 60;
        isCountdownRunning = true;

        // 開始倒數計時
        countdownInterval = setInterval(() => {
            timeLeft--;

            // 計算分鐘和秒數
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;

            // 更新顯示
            countdownTimerEl.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

            // 當時間到0時停止計時
            if (timeLeft <= 0) {
                clearInterval(countdownInterval);
                countdownInterval = null;
                isCountdownRunning = false;
                countdownTimerEl.textContent = '時間到！';

                // 播放倒數結束音效（如果設定不是"不播放"）
                if (musicSettings.countdownEndSound !== 'none') {
                    playSound('countdownEnd', musicSettings.volume, musicSettings.duration);
                }

                // 可選：添加時間到的提醒效果
                countdownTimerEl.style.animation = 'pulse 1s infinite';
            }
        }, 1000);
    }

    /**
     * 停止倒數計時器
     */
    function stopCountdown() {
        if (countdownInterval) {
            clearInterval(countdownInterval);
            countdownInterval = null;
        }
        isCountdownRunning = false;
        currentCountdownClassId = null;
        // 重置為設置的倒數時間
        countdownTimerEl.textContent = `${countdownMinutes}:00`;
        countdownTimerEl.style.animation = '';
    }

    /**
     * 更新沒有課程時的顯示訊息
     * @param {string} currentDay - 當前星期幾
     */
    function updateNoClassMessage(currentDay) {
        const noClassTitle = noClassViewEl.querySelector('h2');
        const noClassText = noClassViewEl.querySelector('p');

        if (currentDay === 'Saturday' || currentDay === 'Sunday') {
            // 週末顯示
            const dayName = currentDay === 'Saturday' ? '星期六' : '星期日';
            noClassTitle.textContent = `今天是${dayName}！`;
            noClassText.textContent = '週末愉快！好好休息或安排自己的學習時間吧！';
        } else {
            // 平日顯示
            noClassTitle.textContent = '現在是下課時間！';
            noClassText.textContent = '好好休息一下吧！';
        }
    }

    /**
     * Updates the UI with the current and next class information.
     * @param {object} currentClass - The class that is currently active.
     * @param {object} nextClass - The next upcoming class.
     * @param {string} currentDay - The current day of the week.
     */
    function updateUI(currentClass, nextClass, currentDay) {
        const isCuteTheme = document.body.classList.contains('theme-cute');
        if (currentClass) {
            noClassViewEl.classList.add('hidden');
            classViewEl.classList.remove('hidden');
            currentSubjectEl.textContent = currentClass.subject;
            currentTasksEl.innerHTML = currentClass.tasks.map(task => `<li>${task}</li>`).join('');
            
            // 設定科目顏色
            const colors = subjectColors[currentClass.subject] || subjectColors['綜合活動'];
            if (!isCuteTheme) {
                classViewEl.style.backgroundColor = colors.background;
                classViewEl.style.borderLeftColor = colors.primary;
                currentSubjectEl.style.color = colors.primary;
            } else {
                // 可愛風不覆蓋卡片與文字的漸層樣式
                classViewEl.style.backgroundColor = '';
                classViewEl.style.borderLeftColor = '';
                currentSubjectEl.style.color = '';
            }

            // 更新進度指示器
            updateProgress(currentClass);

            // 只有在課程變化時才啟動倒數計時器
            if (currentCountdownClassId !== currentClass.id) {
                currentCountdownClassId = currentClass.id;
                startCountdown();
            }
        } else {
            classViewEl.classList.add('hidden');
            noClassViewEl.classList.remove('hidden');

            // 根據當前日期更新顯示內容
            updateNoClassMessage(currentDay);

            // 重置顏色
            classViewEl.style.backgroundColor = '';
            classViewEl.style.borderLeftColor = '';
            currentSubjectEl.style.color = '';

            // 重置進度指示器
            if (progressFillEl && progressTextEl) {
                progressFillEl.style.width = '0%';
                progressTextEl.textContent = '0%';
            }

            // 停止倒數計時器
            stopCountdown();

            // 重置課程ID
            currentCountdownClassId = null;
        }

        if (nextClass) {
            nextSubjectEl.textContent = nextClass.subject;
            nextTimeEl.textContent = nextClass.start;

            // 設定下一節課的顏色
            const nextColors = subjectColors[nextClass.subject] || subjectColors['綜合活動'];
            if (!isCuteTheme) {
                nextSubjectEl.style.color = nextColors.primary;
            } else {
                nextSubjectEl.style.color = '';
            }
        } else {
            // 沒有下一節課時，根據當前日期顯示不同訊息
            if (currentDay === 'Saturday' || currentDay === 'Sunday') {
                // 週末時顯示下週一的第一節課
                const mondayClasses = schedule.filter(c => c.day === 'Monday');
                if (mondayClasses.length > 0) {
                    const firstMondayClass = mondayClasses.sort((a, b) => a.start.localeCompare(b.start))[0];
                    nextSubjectEl.textContent = `下週一：${firstMondayClass.subject}`;
                    nextTimeEl.textContent = firstMondayClass.start;
                    const nextColors = subjectColors[firstMondayClass.subject] || subjectColors['綜合活動'];
                    nextSubjectEl.style.color = nextColors.primary;
                } else {
                    nextSubjectEl.textContent = '週末愉快';
                    nextTimeEl.textContent = '--:--';
                    nextSubjectEl.style.color = '';
                }
            } else {
                nextSubjectEl.textContent = '今天沒有課了';
                nextTimeEl.textContent = '--:--';
                nextSubjectEl.style.color = '';
            }
        }
    }

    /**
     * 檢查並播放特殊時段音樂（掃地、午餐工作、午餐、午休、放學準備）
     */
    function checkSpecialTimeMusic() {
        const now = new Date();
        const currentTime = now.getHours().toString().padStart(2, '0') + ':' +
                           now.getMinutes().toString().padStart(2, '0');

        // 檢查午餐工作時間
        if (musicSettings.lunchPrepMusic !== 'none' &&
            currentTime >= musicSettings.lunchPrepTimeStart &&
            currentTime <= musicSettings.lunchPrepTimeEnd) {

            // 檢查是否已經播放過（避免重複播放）
            const lunchPrepKey = `lunchPrep_${now.toDateString()}_${musicSettings.lunchPrepTimeStart}`;
            if (!localStorage.getItem(lunchPrepKey)) {
                playLunchPrepMusic();
                localStorage.setItem(lunchPrepKey, 'played');
            }
        }

        // 檢查中午時間
        if (musicSettings.lunchMusic !== 'none' &&
            currentTime >= musicSettings.lunchTimeStart &&
            currentTime <= musicSettings.lunchTimeEnd) {

            // 檢查是否已經播放過（避免重複播放）
            const lunchKey = `lunch_${now.toDateString()}_${musicSettings.lunchTimeStart}`;
            if (!localStorage.getItem(lunchKey)) {
                playLunchMusic();
                localStorage.setItem(lunchKey, 'played');
            }
        }

        // 檢查午休時間
        if (musicSettings.napMusic !== 'none' &&
            currentTime >= musicSettings.napTimeStart &&
            currentTime <= musicSettings.napTimeEnd) {

            // 檢查是否已經播放過（避免重複播放）
            const napKey = `nap_${now.toDateString()}_${musicSettings.napTimeStart}`;
            if (!localStorage.getItem(napKey)) {
                playNapMusic();
                localStorage.setItem(napKey, 'played');
            }
        }

        // 檢查掃地時間
        if (musicSettings.cleaningMusic !== 'none' &&
            currentTime >= musicSettings.cleaningTimeStart &&
            currentTime <= musicSettings.cleaningTimeEnd) {

            // 檢查是否已經播放過（避免重複播放）
            const cleaningKey = `cleaning_${now.toDateString()}_${musicSettings.cleaningTimeStart}`;
            if (!localStorage.getItem(cleaningKey)) {
                playCleaningMusic();
                localStorage.setItem(cleaningKey, 'played');
            }
        }

        // 檢查放學準備時間
        if (musicSettings.dismissalMusic !== 'none' &&
            currentTime >= musicSettings.dismissalTimeStart &&
            currentTime <= musicSettings.dismissalTimeEnd) {

            // 檢查是否已經播放過（避免重複播放）
            const dismissalKey = `dismissal_${now.toDateString()}_${musicSettings.dismissalTimeStart}`;
            if (!localStorage.getItem(dismissalKey)) {
                playDismissalMusic();
                localStorage.setItem(dismissalKey, 'played');
            }
        }
    }

    /**
     * 播放午餐工作音樂
     */
    async function playLunchPrepMusic() {
        if (!musicSettings.enabled || musicSettings.lunchPrepMusic === 'none') return;

        try {
            if (musicSettings.lunchPrepMusic === 'custom' && musicSettings.customSounds.lunchPrep) {
                // 播放自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.lunchPrep;
                audio.volume = musicSettings.volume;
                audio.play();

                // 設定播放時長
                setTimeout(() => {
                    audio.pause();
                    audio.currentTime = 0;
                }, musicSettings.duration * 1000);
            } else {
                // 播放預設音效
                playSound('lunchPrep', musicSettings.volume, musicSettings.duration);
            }

            // 顯示通知
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('🍽️ 午餐工作時間', {
                    body: '開始準備午餐，服務同學！',
                    icon: 'assets/icon.png'
                });
            }
        } catch (error) {
            console.error('播放午餐工作音樂時發生錯誤:', error);
        }
    }

    /**
     * 播放中午音樂
     */
    async function playLunchMusic() {
        if (!musicSettings.enabled || musicSettings.lunchMusic === 'none') return;

        try {
            if (musicSettings.lunchMusic === 'custom' && musicSettings.customSounds.lunch) {
                // 播放自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.lunch;
                audio.volume = musicSettings.volume;
                audio.play();

                // 設定播放時長
                setTimeout(() => {
                    audio.pause();
                    audio.currentTime = 0;
                }, musicSettings.duration * 1000);
            } else {
                // 播放預設音效
                playSound('lunch', musicSettings.volume, musicSettings.duration);
            }

            // 顯示通知
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('🍽️ 中午用餐時間', {
                    body: '享受美味的午餐時光！',
                    icon: 'assets/icon.png'
                });
            }
        } catch (error) {
            console.error('播放中午音樂時發生錯誤:', error);
        }
    }

    /**
     * 播放午休音樂
     */
    async function playNapMusic() {
        if (!musicSettings.enabled || musicSettings.napMusic === 'none') return;

        try {
            if (musicSettings.napMusic === 'custom' && musicSettings.customSounds.nap) {
                // 播放自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.nap;
                audio.volume = musicSettings.volume;
                audio.play();

                // 設定播放時長
                setTimeout(() => {
                    audio.pause();
                    audio.currentTime = 0;
                }, musicSettings.duration * 1000);
            } else {
                // 播放預設音效
                playSound('nap', musicSettings.volume, musicSettings.duration);
            }

            // 顯示通知
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('😴 午休時間', {
                    body: '好好休息，為下午的課程充電！',
                    icon: 'assets/icon.png'
                });
            }
        } catch (error) {
            console.error('播放午休音樂時發生錯誤:', error);
        }
    }

    /**
     * 播放放學準備音樂
     */
    async function playDismissalMusic() {
        if (!musicSettings.enabled || musicSettings.dismissalMusic === 'none') return;

        try {
            if (musicSettings.dismissalMusic === 'custom' && musicSettings.customSounds.dismissal) {
                // 播放自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.dismissal;
                audio.volume = musicSettings.volume;
                audio.play();

                // 設定播放時長
                setTimeout(() => {
                    audio.pause();
                    audio.currentTime = 0;
                }, musicSettings.duration * 1000);
            } else {
                // 播放預設音效
                playSound('dismissal', musicSettings.volume, musicSettings.duration);
            }

            // 顯示通知
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('🎒 放學準備時間', {
                    body: '整理書包，準備開心回家！',
                    icon: 'assets/icon.png'
                });
            }
        } catch (error) {
            console.error('播放放學準備音樂時發生錯誤:', error);
        }
    }

    /**
     * 播放掃地音樂
     */
    async function playCleaningMusic() {
        if (!musicSettings.enabled || musicSettings.cleaningMusic === 'none') return;

        try {
            if (musicSettings.cleaningMusic === 'custom' && musicSettings.customSounds.cleaning) {
                // 播放自訂音樂
                const audio = new Audio();
                audio.src = musicSettings.customSounds.cleaning;
                audio.volume = musicSettings.volume;
                audio.play();

                // 設定播放時長
                setTimeout(() => {
                    audio.pause();
                    audio.currentTime = 0;
                }, musicSettings.duration * 1000);
            } else {
                // 播放預設音效
                playSound('cleaning', musicSettings.volume, musicSettings.duration);
            }

            // 顯示通知
            if ('Notification' in window && Notification.permission === 'granted') {
                new Notification('🧹 掃地時間', {
                    body: '開始整理環境，保持教室整潔！',
                    icon: 'assets/icon.png'
                });
            }
        } catch (error) {
            console.error('播放掃地音樂時發生錯誤:', error);
        }
    }

    /**
     * Sends a desktop notification.
     * @param {object} currentClass - The class to notify about.
     */
    function sendNotification(currentClass) {
        // 播放上課鈴聲（如果設定不是"不播放"）
        if (musicSettings.classStartSound !== 'none') {
            playSound('classStart', musicSettings.volume, musicSettings.duration);
        }
        
        // 檢查是否在 Electron 環境中
        if (typeof window !== 'undefined' && window.require) {
            // Electron 環境，通知主進程顯示視窗
            const { ipcRenderer } = window.require('electron');
            ipcRenderer.invoke('class-started', currentClass);
        }
        
        // 同時發送瀏覽器通知作為備用
        if ('Notification' in window) {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    const notification = new Notification(`上課囉！現在是 ${currentClass.subject} 時間`, {
                        body: `該準備：\n${currentClass.tasks.join('\n')}`,
                        icon: 'assets/icon.png'
                    });
                }
            });
        }
    }

    /**
     * 計算課程進度百分比
     * @param {object} currentClass - 當前課程
     * @returns {number} 進度百分比 (0-100)
     */
    function calculateProgress(currentClass) {
        if (!currentClass) return 0;
        
        const now = new Date();
        const startTime = new Date();
        const endTime = new Date();
        
        // 解析開始和結束時間
        const [startHour, startMin] = currentClass.start.split(':').map(Number);
        const [endHour, endMin] = currentClass.end.split(':').map(Number);
        
        startTime.setHours(startHour, startMin, 0, 0);
        endTime.setHours(endHour, endMin, 0, 0);
        
        const totalDuration = endTime - startTime;
        const elapsed = now - startTime;
        
        if (elapsed <= 0) return 0;
        if (elapsed >= totalDuration) return 100;
        
        return Math.round((elapsed / totalDuration) * 100);
    }

    /**
     * 更新進度指示器
     * @param {object} currentClass - 當前課程
     */
    function updateProgress(currentClass) {
        const progress = calculateProgress(currentClass);
        
        if (progressFillEl && progressTextEl) {
            progressFillEl.style.width = `${progress}%`;
            progressTextEl.textContent = `${progress}%`;
        }
    }

    /**
     * 計算今日課程統計
     */
    function updateDailyStats() {
        const now = new Date();
        const currentDay = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][now.getDay()];
        const currentTime = now.toTimeString().slice(0, 5);
        
        // 獲取今日課程
        const todayClasses = schedule.filter(c => c.day === currentDay);
        const totalClasses = todayClasses.length;
        
        // 計算已完成和剩餘課程
        let completedClasses = 0;
        let remainingClasses = 0;
        
        todayClasses.forEach(c => {
            if (currentTime >= c.end) {
                completedClasses++;
            } else if (currentTime < c.start) {
                remainingClasses++;
            }
        });
        
        // 計算今日進度
        const dailyProgress = totalClasses > 0 ? Math.round((completedClasses / totalClasses) * 100) : 0;
        
        // 更新統計顯示
        if (totalClassesEl) totalClassesEl.textContent = totalClasses;
        if (completedClassesEl) completedClassesEl.textContent = completedClasses;
        if (remainingClassesEl) remainingClassesEl.textContent = remainingClasses;
        if (currentProgressEl) currentProgressEl.textContent = `${dailyProgress}%`;
    }

    // --- 字體大小調整功能 ---

    /**
     * 設定字體大小
     * @param {string} size - 字體大小 ('small', 'medium', 'large')
     */
    function setFontSize(size) {
        console.log('設定字體大小:', size); // 調試用
        
        const settings = fontSizeSettings[size];
        if (!settings) {
            console.error('無效的字體大小:', size);
            return;
        }

        // 更新 CSS 變數
        Object.entries(settings).forEach(([property, value]) => {
            document.documentElement.style.setProperty(property, value);
            console.log('設定 CSS 變數:', property, '=', value); // 調試用
        });

        // 更新按鈕狀態
        fontSizeBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.size === size) {
                btn.classList.add('active');
            }
        });

        // 儲存設定
        saveFontSizeSettings(size);
        
        console.log('字體大小設定完成'); // 調試用
    }

    /**
     * 載入儲存的字體大小設定
     */
    async function loadFontSize() {
        let savedSize = 'medium';
        
        if (isElectron) {
            try {
                const { ipcRenderer } = window.require('electron');
                const result = await ipcRenderer.invoke('load-settings');
                
                if (result.success && result.data && result.data.fontSize) {
                    savedSize = result.data.fontSize;
                    console.log('從檔案載入字體大小設定:', savedSize);
                } else {
                    console.log('使用預設字體大小設定');
                }
            } catch (error) {
                console.error('從檔案載入字體設定失敗:', error);
                // 降級到 localStorage
                savedSize = localStorage.getItem('fontSize') || 'medium';
            }
        } else {
            // 瀏覽器環境：使用 localStorage
            savedSize = localStorage.getItem('fontSize') || 'medium';
        }
        
        console.log('載入字體大小設定:', savedSize); // 調試用
        setFontSize(savedSize);
    }

    /**
     * 儲存字體大小設定
     */
    async function saveFontSizeSettings(size) {
        if (isElectron) {
            try {
                const { ipcRenderer } = window.require('electron');
                
                // 載入現有設定
                const currentResult = await ipcRenderer.invoke('load-settings');
                let allSettings = {};
                
                if (currentResult.success && currentResult.data) {
                    allSettings = currentResult.data;
                }
                
                // 更新字體設定
                allSettings.fontSize = size;
                
                // 儲存完整設定
                await ipcRenderer.invoke('save-settings', allSettings);
                console.log('字體設定已儲存到檔案');
            } catch (error) {
                console.error('儲存字體設定到檔案失敗:', error);
                // 降級到 localStorage
                localStorage.setItem('fontSize', size);
            }
        } else {
            // 瀏覽器環境：使用 localStorage
            localStorage.setItem('fontSize', size);
        }
    }

    // --- 匯出功能相關函數 ---

    /**
     * 準備匯出資料
     * @param {boolean} includeWeekend - 是否包含週末
     * @param {boolean} includeTasks - 是否包含任務清單
     * @param {boolean} includeSpecialTimes - 是否包含特殊時段
     * @returns {Array} 處理後的課表資料
     */
    function prepareExportData(includeWeekend = true, includeTasks = true, includeSpecialTimes = true) {
        let exportData = [...schedule];

        // 過濾週末課程
        if (!includeWeekend) {
            exportData = exportData.filter(item => 
                item.day !== 'Saturday' && item.day !== 'Sunday'
            );
        }

        // 處理任務清單
        if (!includeTasks) {
            exportData = exportData.map(item => {
                const { tasks, ...itemWithoutTasks } = item;
                return itemWithoutTasks;
            });
        }

        // 添加特殊時段（如果選擇包含）
        if (includeSpecialTimes) {
            const specialTimes = [
                {
                    id: 'cleaning',
                    day: 'Monday', // 代表每個工作日
                    start: musicSettings.cleaningTimeStart,
                    end: musicSettings.cleaningTimeEnd,
                    subject: '🧹 掃地時間',
                    type: 'special',
                    tasks: includeTasks ? ['整理環境', '保持教室整潔', '準備開始一天的課程'] : undefined
                },
                {
                    id: 'lunch-prep',
                    day: 'Monday',
                    start: musicSettings.lunchPrepTimeStart,
                    end: musicSettings.lunchPrepTimeEnd,
                    subject: '🍽️ 午餐工作時間',
                    type: 'special',
                    tasks: includeTasks ? ['準備午餐', '服務同學', '協助分發餐具'] : undefined
                },
                {
                    id: 'lunch',
                    day: 'Monday',
                    start: musicSettings.lunchTimeStart,
                    end: musicSettings.lunchTimeEnd,
                    subject: '🍽️ 午餐時間',
                    type: 'special',
                    tasks: includeTasks ? ['享用美味午餐', '補充體力', '與同學愉快交流'] : undefined
                },
                {
                    id: 'nap',
                    day: 'Monday',
                    start: musicSettings.napTimeStart,
                    end: musicSettings.napTimeEnd,
                    subject: '😴 午休時間',
                    type: 'special',
                    tasks: includeTasks ? ['安靜休息', '為下午課程做準備', '充分放鬆身心'] : undefined
                },
                {
                    id: 'dismissal',
                    day: 'Monday',
                    start: musicSettings.dismissalTimeStart,
                    end: musicSettings.dismissalTimeEnd,
                    subject: '🎒 放學準備時間',
                    type: 'special',
                    tasks: includeTasks ? ['整理書包', '收拾用品', '準備開心回家'] : undefined
                }
            ];

            // 將特殊時段添加到每個工作日
            const workDays = includeWeekend ? 
                ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'] :
                ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

            specialTimes.forEach(specialTime => {
                workDays.forEach(day => {
                    if (includeWeekend || (day !== 'Saturday' && day !== 'Sunday')) {
                        exportData.push({
                            ...specialTime,
                            id: `${specialTime.id}-${day}`,
                            day: day
                        });
                    }
                });
            });
        }

        // 按星期和時間排序
        const dayOrder = { 'Monday': 1, 'Tuesday': 2, 'Wednesday': 3, 'Thursday': 4, 'Friday': 5, 'Saturday': 6, 'Sunday': 7 };
        exportData.sort((a, b) => {
            if (a.day !== b.day) {
                return dayOrder[a.day] - dayOrder[b.day];
            }
            return a.start.localeCompare(b.start);
        });

        return exportData;
    }

    /**
     * 匯出為 JSON 格式
     * @param {string} filename - 檔案名稱
     * @param {Array} data - 要匯出的資料
     */
    function exportToJSON(filename, data) {
        const jsonData = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonData], { type: 'application/json' });
        downloadFile(blob, `${filename}.json`);
    }

    /**
     * 匯出為 CSV 格式
     * @param {string} filename - 檔案名稱
     * @param {Array} data - 要匯出的資料
     */
    function exportToCSV(filename, data) {
        const dayNames = {
            'Monday': '星期一',
            'Tuesday': '星期二',
            'Wednesday': '星期三',
            'Thursday': '星期四',
            'Friday': '星期五',
            'Saturday': '星期六',
            'Sunday': '星期日'
        };

        // CSV 標題行
        let csv = 'ID,星期,開始時間,結束時間,科目,類型,任務清單\n';

        // 轉換資料行
        data.forEach(item => {
            const id = item.id || '';
            const day = dayNames[item.day] || item.day;
            const start = item.start || '';
            const end = item.end || '';
            const subject = item.subject || '';
            const type = item.type || '一般課程';
            const tasks = item.tasks ? item.tasks.join(' | ') : '';

            // 轉義 CSV 特殊字符
            const escapeCsvValue = (value) => {
                if (typeof value !== 'string') return value;
                if (value.includes(',') || value.includes('"') || value.includes('\n')) {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return value;
            };

            csv += `${escapeCsvValue(id)},${escapeCsvValue(day)},${escapeCsvValue(start)},${escapeCsvValue(end)},${escapeCsvValue(subject)},${escapeCsvValue(type)},${escapeCsvValue(tasks)}\n`;
        });

        const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8' }); // 添加 BOM 以支援中文
        downloadFile(blob, `${filename}.csv`);
    }

    /**
     * 匯出為 PDF 格式
     * @param {string} filename - 檔案名稱
     * @param {Array} data - 要匯出的資料
     */
    async function exportToPDF(filename, data) {
        try {
            // 動態載入 jsPDF 庫
            if (!window.jsPDF) {
                await loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js');
            }

            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();

            // 載入中文字體支援（使用思源黑體的替代方案）
            doc.setFont('helvetica');
            doc.setFontSize(16);

            // 標題
            doc.text('課程表', 105, 20, { align: 'center' });

            // 日期
            doc.setFontSize(10);
            doc.text(`匯出日期: ${new Date().toLocaleDateString('zh-TW')}`, 20, 35);

            const dayNames = {
                'Monday': '星期一',
                'Tuesday': '星期二',
                'Wednesday': '星期三',
                'Thursday': '星期四',
                'Friday': '星期五',
                'Saturday': '星期六',
                'Sunday': '星期日'
            };

            let yPosition = 50;
            let currentDay = '';

            data.forEach(item => {
                const day = dayNames[item.day] || item.day;
                
                // 如果是新的一天，添加日期標題
                if (day !== currentDay) {
                    currentDay = day;
                    yPosition += 10;
                    doc.setFontSize(14);
                    doc.setFont('helvetica', 'bold');
                    doc.text(day, 20, yPosition);
                    doc.setFont('helvetica', 'normal');
                    yPosition += 8;
                }

                // 檢查是否需要新頁面
                if (yPosition > 270) {
                    doc.addPage();
                    yPosition = 20;
                }

                doc.setFontSize(10);
                
                // 課程資訊
                const timeRange = `${item.start} - ${item.end}`;
                const subject = item.subject || '';
                
                doc.text(`  ${timeRange}  ${subject}`, 25, yPosition);
                yPosition += 6;

                // 任務清單
                if (item.tasks && item.tasks.length > 0) {
                    doc.setFontSize(8);
                    item.tasks.forEach(task => {
                        if (yPosition > 270) {
                            doc.addPage();
                            yPosition = 20;
                        }
                        doc.text(`    • ${task}`, 30, yPosition);
                        yPosition += 4;
                    });
                    doc.setFontSize(10);
                }
                
                yPosition += 3;
            });

            doc.save(`${filename}.pdf`);
        } catch (error) {
            console.error('PDF 匯出失敗:', error);
            alert('PDF 匯出失敗，請檢查網路連線或稍後再試。');
        }
    }

    /**
     * 匯出為 Excel 格式
     * @param {string} filename - 檔案名稱
     * @param {Array} data - 要匯出的資料
     */
    async function exportToExcel(filename, data) {
        try {
            // 動態載入 SheetJS 庫
            if (!window.XLSX) {
                await loadScript('https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js');
            }

            const dayNames = {
                'Monday': '星期一',
                'Tuesday': '星期二',
                'Wednesday': '星期三',
                'Thursday': '星期四',
                'Friday': '星期五',
                'Saturday': '星期六',
                'Sunday': '星期日'
            };

            // 準備 Excel 資料
            const excelData = data.map(item => ({
                'ID': item.id || '',
                '星期': dayNames[item.day] || item.day,
                '開始時間': item.start || '',
                '結束時間': item.end || '',
                '科目': item.subject || '',
                '類型': item.type || '一般課程',
                '任務清單': item.tasks ? item.tasks.join(' | ') : ''
            }));

            // 創建工作簿
            const wb = window.XLSX.utils.book_new();
            const ws = window.XLSX.utils.json_to_sheet(excelData);

            // 設定欄寬
            const colWidths = [
                { wch: 8 },  // ID
                { wch: 10 }, // 星期
                { wch: 10 }, // 開始時間
                { wch: 10 }, // 結束時間
                { wch: 15 }, // 科目
                { wch: 12 }, // 類型
                { wch: 40 }  // 任務清單
            ];
            ws['!cols'] = colWidths;

            // 添加工作表
            window.XLSX.utils.book_append_sheet(wb, ws, '課程表');

            // 匯出檔案
            window.XLSX.writeFile(wb, `${filename}.xlsx`);
        } catch (error) {
            console.error('Excel 匯出失敗:', error);
            alert('Excel 匯出失敗，請檢查網路連線或稍後再試。');
        }
    }

    /**
     * 動態載入 JavaScript 庫
     * @param {string} src - 腳本來源 URL
     * @returns {Promise} 載入結果的 Promise
     */
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * 下載檔案
     * @param {Blob} blob - 檔案資料
     * @param {string} filename - 檔案名稱
     */
    function downloadFile(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }

    /**
     * 處理匯出格式選擇
     * @param {string} format - 匯出格式 ('json', 'csv', 'pdf', 'excel')
     */
    async function handleExport(format) {
        const includeWeekend = includeWeekendCheckbox?.checked ?? true;
        const includeTasks = includeTasksCheckbox?.checked ?? true;
        const includeSpecialTimes = includeSpecialTimesCheckbox?.checked ?? true;
        const filename = exportFilenameInput?.value?.trim() || '課程表';

        const data = prepareExportData(includeWeekend, includeTasks, includeSpecialTimes);

        if (data.length === 0) {
            alert('沒有可匯出的資料！');
            return;
        }

        try {
            switch (format) {
                case 'json':
                    exportToJSON(filename, data);
                    break;
                case 'csv':
                    exportToCSV(filename, data);
                    break;
                case 'pdf':
                    await exportToPDF(filename, data);
                    break;
                case 'excel':
                    await exportToExcel(filename, data);
                    break;
                default:
                    alert('不支援的匯出格式！');
                    return;
            }

            hideModal(exportModal);
            alert(`課程表已成功匯出為 ${format.toUpperCase()} 格式！`);
        } catch (error) {
            console.error('匯出失敗:', error);
            alert('匯出失敗，請檢查網路連線或稍後再試。');
        }
    }

    // --- 匯入功能相關函數 ---

    /**
     * 驗證課表資料格式
     * @param {Array} data - 要驗證的資料
     * @returns {boolean} 驗證結果
     */
    function validateScheduleData(data) {
        if (!Array.isArray(data)) {
            throw new Error('課表資料必須是陣列格式');
        }

        if (data.length === 0) {
            throw new Error('課表資料不能為空');
        }

        // 必需欄位
        const requiredFields = ['day', 'start', 'end', 'subject'];
        const validDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

        data.forEach((item, index) => {
            // 檢查必需欄位
            requiredFields.forEach(field => {
                if (!item.hasOwnProperty(field) || !item[field]) {
                    throw new Error(`第 ${index + 1} 筆資料缺少必需欄位: ${field}`);
                }
            });

            // 檢查星期格式
            if (!validDays.includes(item.day)) {
                throw new Error(`第 ${index + 1} 筆資料的星期格式不正確: ${item.day}`);
            }

            // 檢查時間格式
            const timeRegex = /^([01]?\d|2[0-3]):([0-5]\d)$/;
            if (!timeRegex.test(item.start)) {
                throw new Error(`第 ${index + 1} 筆資料的開始時間格式不正確: ${item.start}`);
            }
            if (!timeRegex.test(item.end)) {
                throw new Error(`第 ${index + 1} 筆資料的結束時間格式不正確: ${item.end}`);
            }

            // 檢查時間邏輯
            if (item.start >= item.end) {
                throw new Error(`第 ${index + 1} 筆資料的開始時間不能晚於或等於結束時間`);
            }

            // 檢查任務清單格式（如果存在）
            if (item.tasks && !Array.isArray(item.tasks)) {
                throw new Error(`第 ${index + 1} 筆資料的任務清單必須是陣列格式`);
            }
        });

        return true;
    }

    /**
     * 清理和標準化課表資料
     * @param {Array} data - 原始資料
     * @returns {Array} 清理後的資料
     */
    function cleanScheduleData(data) {
        return data.map((item, index) => {
            const cleaned = {
                id: item.id || (index + 1),
                day: item.day.trim(),
                start: item.start.trim(),
                end: item.end.trim(),
                subject: item.subject.trim(),
                tasks: Array.isArray(item.tasks) ? 
                    item.tasks.filter(task => task && task.trim()).map(task => task.trim()) : 
                    []
            };

            // 保留其他可能的欄位（如 type）
            if (item.type) {
                cleaned.type = item.type.trim();
            }

            return cleaned;
        });
    }

    /**
     * 儲存課表資料到 localStorage
     * @param {Array} data - 課表資料
     */
    function saveScheduleToLocalStorage(data) {
        try {
            localStorage.setItem('importedSchedule', JSON.stringify(data));
            localStorage.setItem('scheduleImportDate', new Date().toISOString());
        } catch (error) {
            console.warn('無法儲存課表到 localStorage:', error);
        }
    }

    /**
     * 從 localStorage 載入課表資料
     * @returns {Array|null} 課表資料或 null
     */
    function loadScheduleFromLocalStorage() {
        try {
            const data = localStorage.getItem('importedSchedule');
            if (data) {
                return JSON.parse(data);
            }
        } catch (error) {
            console.warn('無法從 localStorage 載入課表:', error);
        }
        return null;
    }

    /**
     * 更新課表資料並刷新介面
     * @param {Array} newScheduleData - 新的課表資料
     */
    function updateScheduleData(newScheduleData) {
        // 更新全域變數
        schedule = newScheduleData;
        
        // 儲存到 localStorage
        saveScheduleToLocalStorage(schedule);
        
        // 重新檢查課程
        checkSchedule();
        updateDailyStats();
        
        // 如果編輯模態是開啟的，重新渲染課程列表
        if (!editModal.classList.contains('hidden')) {
            renderScheduleList();
        }
    }

    /**
     * 處理檔案匯入
     * @param {File} file - 要匯入的檔案
     */
    async function handleFileImport(file) {
        if (!file) {
            alert('請選擇要匯入的檔案！');
            return;
        }

        // 檢查檔案類型
        if (!file.name.toLowerCase().endsWith('.json')) {
            alert('請選擇 JSON 格式的檔案！');
            return;
        }

        // 檢查檔案大小（限制為 5MB）
        if (file.size > 5 * 1024 * 1024) {
            alert('檔案太大！請選擇小於 5MB 的檔案。');
            return;
        }

        try {
            const text = await readFileAsText(file);
            const data = JSON.parse(text);
            
            // 驗證資料格式
            validateScheduleData(data);
            
            // 清理和標準化資料
            const cleanedData = cleanScheduleData(data);
            
            // 確認匯入
            const confirmMessage = `即將匯入 ${cleanedData.length} 筆課程資料，這將替換目前的課表。\n\n確定要繼續嗎？`;
            if (!confirm(confirmMessage)) {
                return;
            }
            
            // 更新課表資料
            updateScheduleData(cleanedData);
            
            // 關閉模態
            hideModal(exportModal);
            
            // 顯示成功訊息
            alert(`成功匯入 ${cleanedData.length} 筆課程資料！\n\n注意：重新整理頁面會恢復原始課表。`);
            
        } catch (error) {
            console.error('匯入失敗:', error);
            
            let errorMessage = '匯入失敗：';
            if (error instanceof SyntaxError) {
                errorMessage += 'JSON 格式不正確，請檢查檔案內容。';
            } else if (error.message) {
                errorMessage += error.message;
            } else {
                errorMessage += '未知錯誤，請檢查檔案格式。';
            }
            
            alert(errorMessage);
        }
    }

    /**
     * 讀取檔案內容為文字
     * @param {File} file - 要讀取的檔案
     * @returns {Promise<string>} 檔案內容
     */
    function readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('檔案讀取失敗'));
            reader.readAsText(file, 'utf-8');
        });
    }

    /**
     * 重置課表為原始資料
     */
    function resetScheduleToOriginal() {
        const confirmMessage = '確定要重置課表為原始資料嗎？\n\n這將清除所有匯入的修改，無法復原。';
        if (!confirm(confirmMessage)) {
            return;
        }

        try {
            // 清除 localStorage 中的匯入資料
            localStorage.removeItem('importedSchedule');
            localStorage.removeItem('scheduleImportDate');

            // 重新載入原始課表資料
            const originalSchedule = getEmbeddedSchedule();
            updateScheduleData(originalSchedule);

            // 關閉模態
            hideModal(exportModal);

            alert('已成功重置課表為原始資料！');
        } catch (error) {
            console.error('重置課表失敗:', error);
            alert('重置課表失敗，請重新整理頁面。');
        }
    }

    /**
     * 清除所有資料並恢復到原始設定
     */
    function clearAllDataAndReset() {
        try {
            // 清除課表相關資料
            localStorage.removeItem('importedSchedule');
            localStorage.removeItem('scheduleImportDate');

            // 清除音樂設定
            localStorage.removeItem('musicSettings');

            // 清除字體大小設定
            localStorage.removeItem('fontSize');

            // 清除主題設定
            localStorage.removeItem('theme');

            // 清除所有音樂播放記錄（使用通配符模式清除）
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (
                    key.startsWith('lunchPrep_') ||
                    key.startsWith('lunch_') ||
                    key.startsWith('nap_') ||
                    key.startsWith('cleaning_') ||
                    key.startsWith('dismissal_')
                )) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));

            // 重新載入原始課表資料
            const originalSchedule = getEmbeddedSchedule();
            updateScheduleData(originalSchedule);

            // 重置音樂設定為預設值
            musicSettings = {
                enabled: true,
                volume: 50,
                classStartSound: 'none',
                countdownEndSound: 'none',
                lunchPrepMusic: 'none',
                lunchMusic: 'none',
                napMusic: 'none',
                dismissalMusic: 'none',
                cleaningMusic: 'none',
                musicDuration: 3,
                lunchPrepTimeStart: '12:20',
                lunchPrepTimeEnd: '12:30',
                lunchTimeStart: '11:50',
                lunchTimeEnd: '12:35',
                napTimeStart: '12:35',
                napTimeEnd: '13:15',
                dismissalTimeStart: '15:45',
                dismissalTimeEnd: '16:00',
                cleaningTimeStart: '07:50',
                cleaningTimeEnd: '08:05'
            };

            // 重置字體大小為預設值
            setFontSize('medium');

            // 重置主題為預設值
            setTheme('cute');

            // 關閉所有模態對話框
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => hideModal(modal));

            // 顯示成功訊息
            alert('已成功清除所有資料並恢復到原始設定！\n\n所有自訂設定已重置為預設值。');

        } catch (error) {
            console.error('清除資料失敗:', error);
            alert('清除資料時發生錯誤，請重新整理頁面。');
        }
    }

    /**
     * 匯出當前課表為 JavaScript 檔案格式
     */
    function exportToJavaScript() {
        const confirmMessage = '即將匯出當前課表為 schedule-data.js 檔案。\n\n您可以用這個檔案替換原始的 schedule-data.js 來永久保存修改。\n\n確定要繼續嗎？';
        if (!confirm(confirmMessage)) {
            return;
        }

        try {
            // 取得當前的課表資料（包含已匯入的修改）
            const currentSchedule = [...schedule];

            // 生成 JavaScript 檔案內容
            const jsContent = generateJavaScriptFileContent(currentSchedule);

            // 創建並下載檔案
            const blob = new Blob([jsContent], { type: 'text/javascript;charset=utf-8' });
            downloadFile(blob, 'schedule-data.js');

            // 關閉模態
            hideModal(exportModal);

            // 顯示說明訊息
            const instructions = `已成功匯出 schedule-data.js 檔案！

使用方法：
1. 下載的檔案已保存到您的下載資料夾
2. 將新的 schedule-data.js 檔案複製到專案資料夾
3. 覆蓋原始的 schedule-data.js 檔案
4. 重新整理網頁即可看到永久的修改

注意：備份原始檔案以防需要回復`;

            alert(instructions);

        } catch (error) {
            console.error('匯出 JS 檔案失敗:', error);
            alert('匯出失敗，請稍後再試。');
        }
    }

    /**
     * 生成 JavaScript 檔案內容
     * @param {Array} scheduleData - 課表資料
     * @returns {string} JavaScript 檔案內容
     */
    function generateJavaScriptFileContent(scheduleData) {
        const currentDate = new Date().toLocaleDateString('zh-TW');
        const header = `/**
 * 課程表資料 - 解決 CORS 問題的內嵌資料
 * 這個檔案包含完整的課程表資料，避免在 file:// 協定下的 fetch CORS 問題
 * 
 * 最後修改日期: ${currentDate}
 * 資料筆數: ${scheduleData.length} 筆課程
 */

window.EMBEDDED_SCHEDULE = `;

        // 將資料轉換為格式化的 JSON 字串
        const jsonString = JSON.stringify(scheduleData, null, 4);

        // 組合完整的檔案內容
        return header + jsonString + ';\n';
    }

    // --- 編輯功能相關函數 ---

    /**
     * 顯示模態對話框
     * @param {HTMLElement} modal - 要顯示的模態對話框元素
     */
    function showModal(modal) {
        modal.classList.remove('hidden');
        setTimeout(() => modal.classList.add('show'), 10);
    }

    /**
     * 隱藏模態對話框
     * @param {HTMLElement} modal - 要隱藏的模態對話框元素
     */
    function hideModal(modal) {
        modal.classList.remove('show');
        setTimeout(() => modal.classList.add('hidden'), 300);
    }

    /**
     * 渲染課程表列表
     */
    function renderScheduleList() {
        scheduleList.innerHTML = '';
        
        if (schedule.length === 0) {
            scheduleList.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">目前沒有課程，請點擊「新增課程」來添加課程。</p>';
            return;
        }

        // 按星期分組
        const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        const dayNames = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];

        daysOfWeek.forEach((day, index) => {
            const dayClasses = schedule.filter(c => c.day === day);
            if (dayClasses.length > 0) {
                const dayHeader = document.createElement('div');
                dayHeader.className = 'day-header';
                dayHeader.innerHTML = `<h3 style="color: #495057; margin: 20px 0 10px 0; padding-bottom: 5px; border-bottom: 2px solid #dee2e6;">${dayNames[index]}</h3>`;
                scheduleList.appendChild(dayHeader);

                // 創建包含課程和休息時間的完整時間表
                const allTimeSlots = [];

                // 添加課程
                dayClasses.forEach(classItem => {
                    allTimeSlots.push({
                        type: 'class',
                        time: classItem.start,
                        item: classItem
                    });
                });

                // 只在平日（週一到週五）添加特殊時段
                if (index < 5) { // 0-4 對應週一到週五
                    // 掃地時間
                    allTimeSlots.push({
                        type: 'break',
                        time: musicSettings.cleaningTimeStart,
                        item: {
                            type: 'cleaning',
                            start: musicSettings.cleaningTimeStart,
                            end: musicSettings.cleaningTimeEnd,
                            description: '整理環境，保持教室整潔'
                        }
                    });

                    // 午餐工作時間
                    allTimeSlots.push({
                        type: 'break',
                        time: musicSettings.lunchPrepTimeStart,
                        item: {
                            type: 'lunchPrep',
                            start: musicSettings.lunchPrepTimeStart,
                            end: musicSettings.lunchPrepTimeEnd,
                            description: '準備午餐，服務同學'
                        }
                    });

                    // 午餐時間
                    allTimeSlots.push({
                        type: 'break',
                        time: musicSettings.lunchTimeStart,
                        item: {
                            type: 'lunch',
                            start: musicSettings.lunchTimeStart,
                            end: musicSettings.lunchTimeEnd,
                            description: '享用美味午餐，補充體力'
                        }
                    });

                    // 午休時間
                    allTimeSlots.push({
                        type: 'break',
                        time: musicSettings.napTimeStart,
                        item: {
                            type: 'nap',
                            start: musicSettings.napTimeStart,
                            end: musicSettings.napTimeEnd,
                            description: '安靜休息，為下午課程做準備'
                        }
                    });

                    // 放學準備時間
                    allTimeSlots.push({
                        type: 'break',
                        time: musicSettings.dismissalTimeStart,
                        item: {
                            type: 'dismissal',
                            start: musicSettings.dismissalTimeStart,
                            end: musicSettings.dismissalTimeEnd,
                            description: '整理書包，準備放學回家'
                        }
                    });
                }

                // 按時間排序
                allTimeSlots.sort((a, b) => a.time.localeCompare(b.time));

                // 渲染所有時間段
                allTimeSlots.forEach(slot => {
                    let scheduleItem;
                    if (slot.type === 'class') {
                        scheduleItem = createScheduleItem(slot.item);
                    } else if (slot.type === 'break') {
                        scheduleItem = createBreakTimeItem(slot.item);
                    }
                    scheduleList.appendChild(scheduleItem);
                });
            }
        });
    }

    /**
     * 創建休息時間項目元素（午餐、午休）
     * @param {object} breakItem - 休息時間資料
     * @returns {HTMLElement} 休息時間項目元素
     */
    function createBreakTimeItem(breakItem) {
        const item = document.createElement('div');
        item.className = 'schedule-item break-time-item';

        const typeInfo = {
            'lunch': {
                title: '🍽️ 午餐時間',
                color: '#28a745'
            },
            'nap': {
                title: '😴 午休時間',
                color: '#6f42c1'
            },
            'dismissal': {
                title: '🎒 放學準備',
                color: '#fd7e14'
            },
            'cleaning': {
                title: '🧹 掃地時間',
                color: '#28a745'
            },
            'lunchPrep': {
                title: '🍽️ 午餐工作',
                color: '#ffc107'
            }
        };

        const info = typeInfo[breakItem.type];

        item.innerHTML = `
            <div class="schedule-item-header" style="background-color: ${info.color}15; border-left: 4px solid ${info.color};">
                <div class="schedule-item-title" style="color: ${info.color}; font-weight: bold;">
                    ${info.title}
                </div>
                <div class="schedule-item-actions">
                    <button class="edit-btn" onclick="editBreakTime('${breakItem.type}')">✏️ 編輯</button>
                </div>
            </div>
            <div class="schedule-item-details">
                <div style="color: ${info.color};">${breakItem.start} - ${breakItem.end}</div>
                <div style="margin-top: 8px; color: #6c757d; font-style: italic;">
                    ${breakItem.description || (breakItem.type === 'lunch' ? '享用美味午餐，補充體力' : breakItem.type === 'nap' ? '安靜休息，為下午課程做準備' : breakItem.type === 'dismissal' ? '整理書包，準備放學回家' : breakItem.type === 'cleaning' ? '整理環境，保持教室整潔' : '準備午餐，服務同學')}
                </div>
            </div>
        `;

        return item;
    }

    /**
     * 創建課程項目元素
     * @param {object} classItem - 課程資料
     * @returns {HTMLElement} 課程項目元素
     */
    function createScheduleItem(classItem) {
        const item = document.createElement('div');
        item.className = 'schedule-item';
        
        const dayNames = {
            'Monday': '星期一',
            'Tuesday': '星期二', 
            'Wednesday': '星期三',
            'Thursday': '星期四',
            'Friday': '星期五',
            'Saturday': '星期六',
            'Sunday': '星期日'
        };

        item.innerHTML = `
            <div class="schedule-item-header">
                <div class="schedule-item-title">${classItem.subject}</div>
                <div class="schedule-item-actions">
                    <button class="edit-btn" onclick="editClass(${classItem.id})">✏️ 編輯</button>
                    <button class="delete-btn" onclick="deleteClass(${classItem.id})">🗑️ 刪除</button>
                </div>
            </div>
            <div class="schedule-item-details">
                <div>${dayNames[classItem.day]} | ${classItem.start} - ${classItem.end}</div>
                <div style="margin-top: 8px;">
                    <strong>任務：</strong>
                    <ul style="margin: 5px 0; padding-left: 20px;">
                        ${classItem.tasks.map(task => `<li>${task}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;

        return item;
    }

    /**
     * 編輯課程
     * @param {number} classId - 課程 ID
     */
    window.editClass = function(classId) {
        const classItem = schedule.find(c => c.id === classId);
        if (!classItem) return;

        editingClassId = classId;
        formTitle.textContent = '編輯課程';
        
        // 填充表單資料
        document.getElementById('class-day').value = classItem.day;
        document.getElementById('class-start').value = classItem.start;
        document.getElementById('class-end').value = classItem.end;
        document.getElementById('class-subject').value = classItem.subject;
        document.getElementById('class-tasks').value = classItem.tasks.join('\n');

        hideModal(editModal);
        showModal(classFormModal);
    };

    /**
     * 刪除課程
     * @param {number} classId - 課程 ID
     */
    window.deleteClass = function(classId) {
        if (confirm('確定要刪除這個課程嗎？')) {
            schedule = schedule.filter(c => c.id !== classId);
            renderScheduleList();
        }
    };

    /**
     * 編輯休息時間
     * @param {string} type - 休息時間類型 ('lunch' 或 'nap')
     */
    window.editBreakTime = function(type) {
        const typeInfo = {
            'lunch': {
                title: '編輯午餐時間',
                startTime: musicSettings.lunchTimeStart,
                endTime: musicSettings.lunchTimeEnd,
                description: '享用美味午餐，補充體力'
            },
            'nap': {
                title: '編輯午休時間',
                startTime: musicSettings.napTimeStart,
                endTime: musicSettings.napTimeEnd,
                description: '安靜休息，為下午課程做準備'
            },
            'dismissal': {
                title: '編輯放學準備時間',
                startTime: musicSettings.dismissalTimeStart,
                endTime: musicSettings.dismissalTimeEnd,
                description: '整理書包，準備放學回家'
            },
            'cleaning': {
                title: '編輯掃地時間',
                startTime: musicSettings.cleaningTimeStart,
                endTime: musicSettings.cleaningTimeEnd,
                description: '整理環境，保持教室整潔'
            },
            'lunchPrep': {
                title: '編輯午餐工作時間',
                startTime: musicSettings.lunchPrepTimeStart,
                endTime: musicSettings.lunchPrepTimeEnd,
                description: '準備午餐，服務同學'
            }
        };

        const info = typeInfo[type];
        if (!info) return;

        // 設置表單標題和資料
        breakTimeTitle.textContent = info.title;
        breakStartTimeInput.value = info.startTime;
        breakEndTimeInput.value = info.endTime;
        breakDescriptionInput.value = info.description;

        // 儲存當前編輯的類型
        breakTimeForm.dataset.editingType = type;

        // 隱藏編輯課程表模態，顯示休息時間編輯模態
        hideModal(editModal);
        showModal(breakTimeModal);
    };

    /**
     * 新增課程
     */
    function addNewClass() {
        editingClassId = null;
        formTitle.textContent = '新增課程';
        
        // 清空表單
        classForm.reset();
        
        hideModal(editModal);
        showModal(classFormModal);
    }

    /**
     * 儲存課程表到本地
     */
    function saveSchedule() {
        try {
            // 在實際應用中，這裡會發送請求到伺服器
            // 由於這是純前端應用，我們只能顯示成功訊息
            alert('課程表已更新！\n\n注意：由於這是純前端應用，變更只會儲存在記憶體中。重新整理頁面後會恢復原始資料。\n\n要永久儲存變更，請手動更新 schedule.json 檔案。');
            
            // 更新課程檢查
            checkSchedule();
            hideModal(editModal);
        } catch (error) {
            console.error('儲存課程表失敗:', error);
            alert('儲存課程表失敗，請重試。');
        }
    }

    /**
     * 處理課程表單提交
     * @param {Event} e - 表單提交事件
     */
    function handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(classForm);
        const day = document.getElementById('class-day').value;
        const start = document.getElementById('class-start').value;
        const end = document.getElementById('class-end').value;
        const subject = document.getElementById('class-subject').value.trim();
        const tasksText = document.getElementById('class-tasks').value.trim();

        if (!subject) {
            alert('請輸入科目名稱');
            return;
        }

        if (!tasksText) {
            alert('請輸入任務清單');
            return;
        }

        const tasks = tasksText.split('\n').filter(task => task.trim() !== '');

        if (tasks.length === 0) {
            alert('請至少輸入一個任務');
            return;
        }

        const classData = {
            day,
            start,
            end,
            subject,
            tasks
        };

        if (editingClassId) {
            // 編輯現有課程
            const index = schedule.findIndex(c => c.id === editingClassId);
            if (index !== -1) {
                schedule[index] = { ...schedule[index], ...classData };
            }
        } else {
            // 新增課程
            const newId = Math.max(...schedule.map(c => c.id), 0) + 1;
            schedule.push({ id: newId, ...classData });
        }

        // 重新排序並更新顯示
        schedule.sort((a, b) => a.start.localeCompare(b.start));
        renderScheduleList();
        checkSchedule();

        hideModal(classFormModal);
        showModal(editModal);
    }

    // --- 事件監聽器 ---

    // 匯出按鈕點擊事件
    if (exportScheduleBtn) {
        exportScheduleBtn.addEventListener('click', () => {
            showModal(exportModal);
        });
    }

    // 關閉匯出模態對話框
    if (closeExportModal) {
        closeExportModal.addEventListener('click', () => {
            hideModal(exportModal);
        });
    }

    // 匯出格式按鈕事件
    exportFormatBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const format = btn.dataset.format;
            if (format) {
                handleExport(format);
            }
        });
    });

    // 點擊匯出模態對話框背景關閉
    if (exportModal) {
        exportModal.addEventListener('click', (e) => {
            if (e.target === exportModal) {
                hideModal(exportModal);
            }
        });
    }

    // 匯入檔案按鈕事件
    if (importFileBtn) {
        importFileBtn.addEventListener('click', () => {
            importFileInput.click();
        });
    }

    // 檔案選擇事件
    if (importFileInput) {
        importFileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFileImport(file);
                // 清空 input，允許重複選擇相同檔案
                e.target.value = '';
            }
        });
    }

    // 重置課表按鈕事件
    if (resetScheduleBtn) {
        resetScheduleBtn.addEventListener('click', () => {
            resetScheduleToOriginal();
        });
    }

    // 匯出 JS 檔案按鈕事件
    if (exportJsBtn) {
        exportJsBtn.addEventListener('click', () => {
            exportToJavaScript();
        });
    }

    // 音樂設定按鈕事件
    musicSettingsBtn.addEventListener('click', () => {
        updateMusicSettingsUI();
        showModal(musicSettingsModal);
    });

    // 測試音樂按鈕事件
    testMusicBtn.addEventListener('click', async () => {
        // 檢查是否設定為不播放
        if (musicSettings.classStartSound === 'none') {
            alert('目前上課鈴聲設定為「不播放」，請先在音樂設定中選擇一個音效類型再進行測試。');
            return;
        }
        
        // 初始化音頻上下文（需要用戶互動）
        try {
            await initAudioContext();
            playSound('classStart', musicSettings.volume, musicSettings.duration);
        } catch (error) {
            console.error('測試音樂播放失敗:', error);
            alert('音樂播放失敗，請確認瀏覽器支援音頻播放功能。');
        }
    });

    // 停止音樂按鈕事件
    stopMusicBtn.addEventListener('click', () => {
        stopCurrentMusic();
    });

    // 關閉音樂設定模態框
    closeMusicSettings.addEventListener('click', () => {
        // 重置表單到保存的設定值
        updateMusicSettingsUI();
        hideModal(musicSettingsModal);
    });

    // 取消音樂設定
    cancelMusicSettings.addEventListener('click', () => {
        // 重置表單到保存的設定值
        updateMusicSettingsUI();
        hideModal(musicSettingsModal);
    });

    // 音量滑桿事件
    musicVolumeSlider.addEventListener('input', (e) => {
        const volume = e.target.value / 100;
        // 只更新顯示，不直接修改 musicSettings
        volumeDisplay.textContent = Math.round(volume * 100) + '%';
    });

    // 音樂設定表單提交
    musicSettingsForm.addEventListener('submit', async (e) => {
        e.preventDefault();

        // 驗證音樂播放時長
        const duration = parseInt(musicDurationInput.value, 10);
        if (duration < 1 || duration > 300) {
            alert('音樂播放時長必須在 1-300 秒之間！');
            return;
        }

        // 更新設定
        musicSettings.enabled = enableMusicCheckbox.checked;
        musicSettings.volume = musicVolumeSlider.value / 100;
        musicSettings.classStartSound = classStartSoundSelect.value;
        musicSettings.countdownEndSound = countdownEndSoundSelect.value;
        musicSettings.duration = duration;

        // 更新所有特殊時段音樂設定
        musicSettings.lunchMusic = lunchMusicSelect.value;
        musicSettings.napMusic = napMusicSelect.value;
        musicSettings.dismissalMusic = dismissalMusicSelect.value;
        musicSettings.cleaningMusic = cleaningMusicSelect.value;
        musicSettings.lunchPrepMusic = lunchPrepMusicSelect.value;
        musicSettings.lunchTimeStart = lunchTimeStartInput.value;
        musicSettings.lunchTimeEnd = lunchTimeEndInput.value;
        musicSettings.napTimeStart = napTimeStartInput.value;
        musicSettings.napTimeEnd = napTimeEndInput.value;
        musicSettings.dismissalTimeStart = dismissalTimeStartInput.value;
        musicSettings.dismissalTimeEnd = dismissalTimeEndInput.value;
        musicSettings.cleaningTimeStart = cleaningTimeStartInput.value;
        musicSettings.cleaningTimeEnd = cleaningTimeEndInput.value;
        musicSettings.lunchPrepTimeStart = lunchPrepTimeStartInput.value;
        musicSettings.lunchPrepTimeEnd = lunchPrepTimeEndInput.value;

        // 儲存設定
        try {
            await saveMusicSettings();
            hideModal(musicSettingsModal);
            alert('音樂設定已儲存！');
        } catch (error) {
            console.error('儲存音樂設定失敗:', error);
            alert('儲存音樂設定失敗，請重試。');
        }
    });

    // 自訂音樂檔案選擇事件
    classStartSoundSelect.addEventListener('change', (e) => {
        if (e.target.value === 'custom') {
            classStartFileInput.style.display = 'block';
            classStartFileInput.click();
        } else {
            classStartFileInput.style.display = 'none';
        }
    });

    countdownEndSoundSelect.addEventListener('change', (e) => {
        if (e.target.value === 'custom') {
            countdownEndFileInput.style.display = 'block';
            countdownEndFileInput.click();
        } else {
            countdownEndFileInput.style.display = 'none';
        }
    });

    // 自訂音樂檔案載入事件
    classStartFileInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (file) {
            try {
                const result = await loadCustomSound(file);
                musicSettings.customSounds.classStart = result;
                alert('上課鈴聲已載入！');
            } catch (error) {
                console.error('載入自訂音樂失敗:', error);
                alert('載入音樂檔案失敗，請確認檔案格式正確。');
                classStartSoundSelect.value = musicSettings.classStartSound;
            }
        }
    });

    countdownEndFileInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (file) {
            try {
                const result = await loadCustomSound(file);
                musicSettings.customSounds.countdownEnd = result;
                alert('倒數結束音效已載入！');
            } catch (error) {
                console.error('載入自訂音樂失敗:', error);
                alert('載入音樂檔案失敗，請確認檔案格式正確。');
                countdownEndSoundSelect.value = musicSettings.countdownEndSound;
            }
        }
    });

    // 中午音樂選擇事件
    lunchMusicSelect.addEventListener('change', (e) => {
        if (e.target.value === 'custom') {
            lunchMusicFileInput.style.display = 'block';
            lunchMusicFileInput.click();
        } else {
            lunchMusicFileInput.style.display = 'none';
        }
    });

    // 午休音樂選擇事件
    napMusicSelect.addEventListener('change', (e) => {
        if (e.target.value === 'custom') {
            napMusicFileInput.style.display = 'block';
            napMusicFileInput.click();
        } else {
            napMusicFileInput.style.display = 'none';
        }
    });

    // 放學準備音樂選擇事件
    dismissalMusicSelect.addEventListener('change', (e) => {
        if (e.target.value === 'custom') {
            dismissalMusicFileInput.style.display = 'block';
            dismissalMusicFileInput.click();
        } else {
            dismissalMusicFileInput.style.display = 'none';
        }
    });

    // 掃地音樂選擇事件
    cleaningMusicSelect.addEventListener('change', (e) => {
        if (e.target.value === 'custom') {
            cleaningMusicFileInput.style.display = 'block';
            cleaningMusicFileInput.click();
        } else {
            cleaningMusicFileInput.style.display = 'none';
        }
    });

    // 午餐工作音樂選擇事件
    lunchPrepMusicSelect.addEventListener('change', (e) => {
        if (e.target.value === 'custom') {
            lunchPrepMusicFileInput.style.display = 'block';
            lunchPrepMusicFileInput.click();
        } else {
            lunchPrepMusicFileInput.style.display = 'none';
        }
    });

    // 中午音樂檔案載入
    lunchMusicFileInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (file) {
            try {
                const reader = new FileReader();
                reader.onload = (e) => {
                    musicSettings.customSounds.lunch = e.target.result;
                    alert('中午音樂已載入！');
                };
                reader.readAsDataURL(file);
            } catch (error) {
                console.error('載入中午音樂失敗:', error);
                alert('載入音樂檔案失敗，請確認檔案格式正確。');
                lunchMusicSelect.value = musicSettings.lunchMusic;
            }
        }
    });

    // 午休音樂檔案載入
    napMusicFileInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (file) {
            try {
                const reader = new FileReader();
                reader.onload = (e) => {
                    musicSettings.customSounds.nap = e.target.result;
                    alert('午休音樂已載入！');
                };
                reader.readAsDataURL(file);
            } catch (error) {
                console.error('載入午休音樂失敗:', error);
                alert('載入音樂檔案失敗，請確認檔案格式正確。');
                napMusicSelect.value = musicSettings.napMusic;
            }
        }
    });

    // 放學準備音樂檔案載入
    dismissalMusicFileInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (file) {
            try {
                const reader = new FileReader();
                reader.onload = (e) => {
                    musicSettings.customSounds.dismissal = e.target.result;
                    alert('放學準備音樂已載入！');
                };
                reader.readAsDataURL(file);
            } catch (error) {
                console.error('載入放學準備音樂失敗:', error);
                alert('載入音樂檔案失敗，請確認檔案格式正確。');
                dismissalMusicSelect.value = musicSettings.dismissalMusic;
            }
        }
    });

    // 掃地音樂檔案載入
    cleaningMusicFileInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (file) {
            try {
                const reader = new FileReader();
                reader.onload = (e) => {
                    musicSettings.customSounds.cleaning = e.target.result;
                    alert('掃地音樂已載入！');
                };
                reader.readAsDataURL(file);
            } catch (error) {
                console.error('載入掃地音樂失敗:', error);
                alert('載入音樂檔案失敗，請確認檔案格式正確。');
                cleaningMusicSelect.value = musicSettings.cleaningMusic;
            }
        }
    });

    // 午餐工作音樂檔案載入
    lunchPrepMusicFileInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (file) {
            try {
                const reader = new FileReader();
                reader.onload = (e) => {
                    musicSettings.customSounds.lunchPrep = e.target.result;
                    alert('午餐工作音樂已載入！');
                };
                reader.readAsDataURL(file);
            } catch (error) {
                console.error('載入午餐工作音樂失敗:', error);
                alert('載入音樂檔案失敗，請確認檔案格式正確。');
                lunchPrepMusicSelect.value = musicSettings.lunchPrepMusic;
            }
        }
    });

    // 測試午餐工作音樂按鈕
    testLunchPrepMusicBtn.addEventListener('click', () => {
        const selectedMusic = lunchPrepMusicSelect.value;
        if (selectedMusic === 'none') {
            alert('請先選擇午餐工作音樂類型！');
            return;
        }

        if (selectedMusic === 'custom' && !musicSettings.customSounds.lunchPrep) {
            alert('請先上傳自訂音樂檔案！');
            return;
        }

        // 播放測試音樂
        playTestMusic('lunchPrep', selectedMusic);
    });

    // 測試中午音樂按鈕
    testLunchMusicBtn.addEventListener('click', () => {
        const selectedMusic = lunchMusicSelect.value;
        if (selectedMusic === 'none') {
            alert('請先選擇中午音樂類型！');
            return;
        }

        if (selectedMusic === 'custom' && !musicSettings.customSounds.lunch) {
            alert('請先上傳自訂音樂檔案！');
            return;
        }

        // 播放測試音樂
        playTestMusic('lunch', selectedMusic);
    });

    // 測試午休音樂按鈕
    testNapMusicBtn.addEventListener('click', () => {
        const selectedMusic = napMusicSelect.value;
        if (selectedMusic === 'none') {
            alert('請先選擇午休音樂類型！');
            return;
        }

        if (selectedMusic === 'custom' && !musicSettings.customSounds.nap) {
            alert('請先上傳自訂音樂檔案！');
            return;
        }

        // 播放測試音樂
        playTestMusic('nap', selectedMusic);
    });

    // 測試放學準備音樂按鈕
    testDismissalMusicBtn.addEventListener('click', () => {
        const selectedMusic = dismissalMusicSelect.value;
        if (selectedMusic === 'none') {
            alert('請先選擇放學準備音樂類型！');
            return;
        }

        if (selectedMusic === 'custom' && !musicSettings.customSounds.dismissal) {
            alert('請先上傳自訂音樂檔案！');
            return;
        }

        // 播放測試音樂
        playTestMusic('dismissal', selectedMusic);
    });

    // 測試掃地音樂按鈕
    testCleaningMusicBtn.addEventListener('click', () => {
        const selectedMusic = cleaningMusicSelect.value;
        if (selectedMusic === 'none') {
            alert('請先選擇掃地音樂類型！');
            return;
        }

        if (selectedMusic === 'custom' && !musicSettings.customSounds.cleaning) {
            alert('請先上傳自訂音樂檔案！');
            return;
        }

        // 播放測試音樂
        playTestMusic('cleaning', selectedMusic);
    });

    /**
     * 播放測試音樂
     * @param {string} type - 音樂類型 ('lunch'、'nap'、'dismissal' 或 'cleaning')
     * @param {string} musicType - 音樂風格
     */
    async function playTestMusic(type, musicType) {
        try {
            // 停止當前播放的音樂
            stopCurrentMusic();

            if (musicType === 'custom') {
                // 播放自訂音樂
                let customSound;
                if (type === 'lunch') {
                    customSound = musicSettings.customSounds.lunch;
                } else if (type === 'nap') {
                    customSound = musicSettings.customSounds.nap;
                } else if (type === 'dismissal') {
                    customSound = musicSettings.customSounds.dismissal;
                } else if (type === 'cleaning') {
                    customSound = musicSettings.customSounds.cleaning;
                } else if (type === 'lunchPrep') {
                    customSound = musicSettings.customSounds.lunchPrep;
                }

                if (customSound) {
                    const audio = new Audio();
                    audio.src = customSound;
                    audio.volume = musicSettings.volume;
                    audio.play();

                    // 設定播放時長（使用完整設定時間，最多30秒）
                    const playDuration = Math.min(musicSettings.duration, 30);
                    setTimeout(() => {
                        audio.pause();
                        audio.currentTime = 0;
                    }, playDuration * 1000);
                }
            } else {
                // 播放預設音效（使用完整設定時間，最多30秒）
                const playDuration = Math.min(musicSettings.duration, 30);
                playSound(type, musicSettings.volume, playDuration);
            }

            // 顯示測試提示
            const musicName = type === 'lunch' ? '中午音樂' : type === 'nap' ? '午休音樂' : type === 'dismissal' ? '放學準備音樂' : type === 'cleaning' ? '掃地音樂' : '午餐工作音樂';
            const typeName = getMusicTypeName(musicType);
            const playDuration = Math.min(musicSettings.duration, 30);
            alert(`正在測試 ${musicName}：${typeName}\n播放時間：${playDuration}秒\n\n如需停止播放，請重新整理頁面或等待自動停止`);

        } catch (error) {
            console.error('測試音樂播放失敗:', error);
            alert('音樂播放失敗，請檢查設定。');
        }
    }

    /**
     * 獲取音樂類型的中文名稱
     * @param {string} type - 音樂類型
     * @returns {string} 中文名稱
     */
    function getMusicTypeName(type) {
        const typeNames = {
            'none': '不播放',
            'bell': '經典鈴聲',
            'chime': '輕柔鐘聲',
            'beep': '電子提示音',
            'ding': '清脆鈴聲',
            'gong': '鑼聲',
            'school-bell': '學校鈴聲',
            'soft-chime': '溫和鐘聲',
            'alarm': '警報聲',
            'whistle': '哨聲',
            'beep-beep': '連續嗶聲',
            'notification': '通知音',
            'chime-end': '結束鐘聲',
            'gentle-alert': '溫和提醒',
            'relaxing': '輕鬆音樂',
            'classical': '古典音樂',
            'nature': '自然音效',
            'lullaby': '搖籃曲',
            'ambient': '環境音樂',
            'white-noise': '白噪音',
            'cheerful': '輕快音樂',
            'motivational': '激勵音樂',
            'peaceful': '平和音樂',
            'energetic': '活力音樂',
            'upbeat': '節奏感音樂',
            'custom': '自訂音樂'
        };
        return typeNames[type] || type;
    }

    // 點擊音樂設定模態對話框背景關閉
    musicSettingsModal.addEventListener('click', (e) => {
        if (e.target === musicSettingsModal) {
            // 重置表單到保存的設定值
            updateMusicSettingsUI();
            hideModal(musicSettingsModal);
        }
    });

    // 倒數計時器設置按鈕事件
    countdownSettingsBtn.addEventListener('click', () => {
        // 設置當前值
        countdownMinutesInput.value = countdownMinutes;
        showModal(countdownSettingsModal);
    });

    // 關閉倒數設置模態框
    closeCountdownSettings.addEventListener('click', () => {
        hideModal(countdownSettingsModal);
    });

    // 取消倒數設置
    cancelCountdownSettings.addEventListener('click', () => {
        hideModal(countdownSettingsModal);
    });

    // 倒數設置表單提交
    countdownSettingsForm.addEventListener('submit', (e) => {
        e.preventDefault();

        // 獲取設置的分鐘數
        const minutes = parseInt(countdownMinutesInput.value, 10);
        if (minutes > 0 && minutes <= 60) {
            countdownMinutes = minutes;

            // 如果當前正在倒數計時，重新啟動
            if (!classViewEl.classList.contains('hidden')) {
                stopCountdown();
                startCountdown();
            } else {
                // 否則更新顯示
                countdownTimerEl.textContent = `${countdownMinutes}:00`;
            }

            hideModal(countdownSettingsModal);
        }
    });

    // 字體大小按鈕事件
    fontSizeBtns.forEach(btn => {
        console.log('為按鈕添加事件監聽器:', btn.dataset.size); // 調試用
        btn.addEventListener('click', (e) => {
            console.log('字體大小按鈕被點擊:', btn.dataset.size); // 調試用
            const size = btn.dataset.size;
            setFontSize(size);
        });
    });

    // 休息時間編輯相關事件監聽器
    if (closeBreakTimeModal) {
        closeBreakTimeModal.addEventListener('click', () => {
            hideModal(breakTimeModal);
            showModal(editModal);
        });
    }

    if (cancelBreakTime) {
        cancelBreakTime.addEventListener('click', () => {
            hideModal(breakTimeModal);
            showModal(editModal);
        });
    }

    if (breakTimeForm) {
        breakTimeForm.addEventListener('submit', (e) => {
            e.preventDefault();

            const editingType = breakTimeForm.dataset.editingType;
            const startTime = breakStartTimeInput.value;
            const endTime = breakEndTimeInput.value;
            const description = breakDescriptionInput.value;

            if (editingType === 'lunch') {
                musicSettings.lunchTimeStart = startTime;
                musicSettings.lunchTimeEnd = endTime;
                // 同時更新音樂設定表單中的值
                if (lunchTimeStartInput) lunchTimeStartInput.value = startTime;
                if (lunchTimeEndInput) lunchTimeEndInput.value = endTime;
            } else if (editingType === 'nap') {
                musicSettings.napTimeStart = startTime;
                musicSettings.napTimeEnd = endTime;
                // 同時更新音樂設定表單中的值
                if (napTimeStartInput) napTimeStartInput.value = startTime;
                if (napTimeEndInput) napTimeEndInput.value = endTime;
            } else if (editingType === 'dismissal') {
                musicSettings.dismissalTimeStart = startTime;
                musicSettings.dismissalTimeEnd = endTime;
                // 同時更新音樂設定表單中的值
                if (dismissalTimeStartInput) dismissalTimeStartInput.value = startTime;
                if (dismissalTimeEndInput) dismissalTimeEndInput.value = endTime;
            } else if (editingType === 'cleaning') {
                musicSettings.cleaningTimeStart = startTime;
                musicSettings.cleaningTimeEnd = endTime;
                // 同時更新音樂設定表單中的值
                if (cleaningTimeStartInput) cleaningTimeStartInput.value = startTime;
                if (cleaningTimeEndInput) cleaningTimeEndInput.value = endTime;
            } else if (editingType === 'lunchPrep') {
                musicSettings.lunchPrepTimeStart = startTime;
                musicSettings.lunchPrepTimeEnd = endTime;
                // 同時更新音樂設定表單中的值
                if (lunchPrepTimeStartInput) lunchPrepTimeStartInput.value = startTime;
                if (lunchPrepTimeEndInput) lunchPrepTimeEndInput.value = endTime;
            }

            // 儲存音樂設定
            saveMusicSettings();

            // 重新渲染課程表
            renderScheduleList();

            // 關閉模態並返回編輯課程表
            hideModal(breakTimeModal);
            showModal(editModal);
        });
    }

    // 編輯按鈕點擊事件
    editScheduleBtn.addEventListener('click', () => {
        renderScheduleList();
        showModal(editModal);
    });

    // 清除資料按鈕點擊事件
    if (clearDataBtn) {
        clearDataBtn.addEventListener('click', () => {
            const confirmMessage = '確定要清除所有資料並恢復到原始設定嗎？\n\n這將會：\n• 清除所有自訂課程\n• 恢復原始課表\n• 重置音樂設定\n• 重置字體大小\n• 重置主題設定\n• 清除所有播放記錄\n\n此操作無法復原！';
            if (confirm(confirmMessage)) {
                clearAllDataAndReset();
            }
        });
    }

    // 關閉編輯模態對話框
    closeModal.addEventListener('click', () => {
        hideModal(editModal);
    });

    // 新增課程按鈕
    addClassBtn.addEventListener('click', addNewClass);

    // 儲存課程表按鈕
    saveScheduleBtn.addEventListener('click', saveSchedule);

    // 關閉課程表單模態對話框
    closeFormModal.addEventListener('click', () => {
        hideModal(classFormModal);
    });

    // 取消表單
    cancelForm.addEventListener('click', () => {
        hideModal(classFormModal);
        showModal(editModal);
    });

    // 課程表單提交
    classForm.addEventListener('submit', handleFormSubmit);

    // 點擊模態對話框背景關閉
    editModal.addEventListener('click', (e) => {
        if (e.target === editModal) {
            hideModal(editModal);
        }
    });

    classFormModal.addEventListener('click', (e) => {
        if (e.target === classFormModal) {
            hideModal(classFormModal);
        }
    });

    // 測試時間相關事件監聽器
    if (timeTestBtn) {
        timeTestBtn.addEventListener('click', () => {
            // 設定當前時間為預設值
            const now = new Date();
            testTimeInput.value = now.toTimeString().slice(0, 5);
            testDateInput.value = now.toISOString().slice(0, 10);
            showModal(timeTestModal);
        });
    }

    if (resetTimeBtn) {
        resetTimeBtn.addEventListener('click', () => {
            // 重置為正常模式
            isTestMode = false;
            testTime = null;
            testDate = null;
            resetTimeBtn.style.display = 'none';
            timeTestBtn.style.display = 'inline-block';

            // 立即更新顯示
            updateTime();
            checkSchedule();
            updateDailyStats();
        });
    }

    if (closeTimeTest) {
        closeTimeTest.addEventListener('click', () => {
            hideModal(timeTestModal);
        });
    }

    if (cancelTimeTest) {
        cancelTimeTest.addEventListener('click', () => {
            hideModal(timeTestModal);
        });
    }

    if (applyTestTime) {
        applyTestTime.addEventListener('click', () => {
            const timeValue = testTimeInput.value;
            const dateValue = testDateInput.value;

            if (!timeValue) {
                alert('請設定測試時間！');
                return;
            }

            // 啟用測試模式
            isTestMode = true;
            testTime = timeValue;
            testDate = dateValue || null;

            // 更新按鈕顯示
            timeTestBtn.style.display = 'none';
            resetTimeBtn.style.display = 'inline-block';

            // 立即更新顯示
            updateTime();
            checkSchedule();
            updateDailyStats();

            hideModal(timeTestModal);
            alert(`測試時間已設定為：${timeValue}${dateValue ? ` (${dateValue})` : ''}`);
        });
    }

    // 點擊測試時間模態對話框背景關閉
    if (timeTestModal) {
        timeTestModal.addEventListener('click', (e) => {
            if (e.target === timeTestModal) {
                hideModal(timeTestModal);
            }
        });
    }

    // --- Initialization ---
    async function init() {
        Notification.requestPermission();
        
        // 先載入原始課表
        await loadSchedule();
        
        // 檢查是否有匯入的課表資料
        const importedSchedule = loadScheduleFromLocalStorage();
        if (importedSchedule && importedSchedule.length > 0) {
            try {
                // 驗證匯入的資料
                validateScheduleData(importedSchedule);
                schedule = importedSchedule;
                console.log('載入已匯入的課表資料:', schedule.length, '筆課程');
            } catch (error) {
                console.warn('匯入的課表資料格式有誤，使用原始課表:', error);
                // 清除有問題的資料
                localStorage.removeItem('importedSchedule');
                localStorage.removeItem('scheduleImportDate');
            }
        }
        
        await loadFontSize(); // 載入字體大小設定
        await loadMusicSettings(); // 載入音樂設定
        loadTheme(); // 載入主題設定
        updateDailyStats(); // 初始化統計資料

        setInterval(updateTime, 1000); // Update clock every second
        setInterval(checkSchedule, 5000); // Check schedule every 5 seconds
        setInterval(updateDailyStats, 1000); // Update daily stats every second
        setInterval(checkSpecialTimeMusic, 60000); // Check special time music every minute
    }

    init();

    // ============ 主題切換 ============
    function setTheme(theme) {
        const body = document.body;
        const isCute = theme === 'cute';
        body.classList.toggle('theme-cute', isCute);
        if (themeToggleBtn) {
            themeToggleBtn.textContent = isCute ? '切換為科技感' : '切換為可愛風';
        }
        localStorage.setItem('theme', theme);
        // 立即刷新 UI 樣式
        const now = new Date();
        const currentDay = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][now.getDay()];
        const currentTime = now.toTimeString().slice(0, 5);
        const todaySchedule = schedule.filter(c => c.day === currentDay);
        const currentClass = todaySchedule.find(c => currentTime >= c.start && currentTime < c.end);
        const nextClass = todaySchedule.find(c => c.start > currentTime);
        updateUI(currentClass, nextClass, currentDay);
    }

    function loadTheme() {
        const saved = localStorage.getItem('theme') || 'cute';
        setTheme(saved);
    }

    if (themeToggleBtn) {
        themeToggleBtn.addEventListener('click', () => {
            const current = document.body.classList.contains('theme-cute') ? 'cute' : 'tech';
            const next = current === 'cute' ? 'tech' : 'cute';
            setTheme(next);
        });
    }
});