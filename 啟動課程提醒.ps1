# 課程提醒小幫手 PowerShell 啟動腳本
param(
    [switch]$Silent = $false
)

# 設定編碼
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "🚀 課程提醒小幫手 - PowerShell 啟動器" -ForegroundColor Cyan
Write-Host "=" * 40

# 切換到腳本所在目錄
$ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptPath

# 檢查 Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js 版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 錯誤：未找到 Node.js" -ForegroundColor Red
    Write-Host "請先安裝 Node.js：https://nodejs.org/" -ForegroundColor Yellow
    if (-not $Silent) { Read-Host "按 Enter 鍵結束" }
    exit 1
}

# 檢查 npm
try {
    $npmVersion = npm --version
    Write-Host "✅ npm 版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 錯誤：npm 無法使用" -ForegroundColor Red
    if (-not $Silent) { Read-Host "按 Enter 鍵結束" }
    exit 1
}

# 檢查依賴
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 首次運行，正在安裝依賴..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 依賴安裝失敗" -ForegroundColor Red
        if (-not $Silent) { Read-Host "按 Enter 鍵結束" }
        exit 1
    }
    Write-Host "✅ 依賴安裝完成" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎯 啟動課程提醒小幫手..." -ForegroundColor Cyan
Write-Host "💡 提示：" -ForegroundColor Yellow
Write-Host "   • 應用會在新視窗中開啟" -ForegroundColor Gray
Write-Host "   • 關閉視窗會最小化到系統托盤" -ForegroundColor Gray
Write-Host "   • 使用 Ctrl+C 完全結束應用" -ForegroundColor Gray
Write-Host ""

# 啟動應用
try {
    npm run dev
} catch {
    Write-Host "❌ 啟動失敗" -ForegroundColor Red
    if (-not $Silent) { Read-Host "按 Enter 鍵結束" }
    exit 1
}
