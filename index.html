<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self'; img-src 'self' data: blob:; media-src 'self' data: blob:;">
    <title>課程提醒小幫手</title>
    <!-- 可愛風字體（Noto Sans TC） -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
</head>
<body class="theme-cute">
    <div class="container">
        <header>
            <div class="header-main">
                <h1>課程提醒小幫手</h1>
                <div id="current-time" class="time">--:--:--</div>
            </div>
        </header>
        <main>
            <div id="no-class-view" class="card hidden">
                <h2>現在是下課時間！</h2>
                <p>好好休息一下吧！</p>
            </div>

            <!-- 課程左右併排佈局 -->
            <div class="course-layout">
                <!-- 當前課程 -->
                <div class="current-course">
                    <div id="class-view" class="card">
                        <div class="card-header">
                            <h2><span class="live-dot"></span>正在上課中</h2>
                            <div class="progress-indicator">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="progress-fill"></div>
                                </div>
                                <span class="progress-text" id="progress-text">0%</span>
                            </div>
                            <div class="countdown-container">
                                <div class="countdown-label">課前準備時間：</div>
                                <div class="countdown-timer" id="countdown-timer">2:00</div>
                                <button class="countdown-settings-btn" id="countdown-settings-btn" title="設置倒數時間">⚙️</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="subject" id="current-subject">--</p>
                            <h3>這節課要做什麼：</h3>
                            <ul id="current-tasks">
                                <li>--</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 下一節課程 -->
                <div class="next-course">
                    <div class="card next-class-card">
                         <div class="card-header">
                            <h2>下一節課</h2>
                        </div>
                        <div class="card-body">
                            <p class="subject"><strong id="next-subject">--</strong></p>
                            <p><span id="next-time">--:--</span> 開始</p>
                            <h3>課前準備：</h3>
                            <ul id="next-tasks">
                                <li>準備課本和用品</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 課程統計卡片 -->
            <div class="card stats-card">
                <div class="card-header">
                    <h2>📊 今日課程統計</h2>
                </div>
                <div class="card-body">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number" id="total-classes">0</div>
                            <div class="stat-label">總課程數</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="completed-classes">0</div>
                            <div class="stat-label">已完成</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="remaining-classes">0</div>
                            <div class="stat-label">剩餘課程</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" id="current-progress">0%</div>
                            <div class="stat-label">今日進度</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 修改和匯出按鈕 -->
            <div class="edit-section">
                <button id="edit-schedule-btn" class="edit-button">
                    <span class="edit-icon">✏️</span>
                    修改課表
                </button>
                <button id="export-schedule-btn" class="edit-button export-button">
                    <span class="edit-icon">📤</span>
                    匯出課表
                </button>
                <button id="clear-data-btn" class="edit-button clear-button">
                    <span class="edit-icon">🗑️</span>
                    清除資料
                </button>
            </div>

            <!-- 控制項 -->
            <div class="controls-section">
                <div class="header-controls">
                    <!-- 字體大小調整控制項 -->
                    <div class="font-size-controls compact">
                        <span class="control-icon" title="字體大小">🔤</span>
                        <div class="font-size-buttons">
                            <button class="font-size-btn" data-size="tiny" title="極小字體">極小</button>
                            <button class="font-size-btn" data-size="small" title="小字體">小</button>
                            <button class="font-size-btn active" data-size="medium" title="中字體">中</button>
                            <button class="font-size-btn" data-size="large" title="大字體">大</button>
                            <button class="font-size-btn" data-size="xlarge" title="超大字體">超大</button>
                        </div>
                    </div>

                    <!-- 音樂設定控制項 -->
                    <div class="music-controls compact">
                        <span class="control-icon" title="音樂設定">🎵</span>
                        <div class="music-buttons">
                            <button class="music-btn" id="music-settings-btn" title="音樂設定">設定</button>
                            <button class="music-btn" id="test-music-btn" title="測試音樂">測試</button>
                            <button class="music-btn" id="stop-music-btn" title="停止音樂" style="display: none;">停止</button>
                        </div>
                    </div>

                </div>
            </div>
            
            <!-- 測試時間控制項 -->
            <div class="test-controls">
                <div class="time-test-controls compact">
                    <span class="control-icon" title="測試時間">🕐</span>
                    <div class="time-test-buttons">
                        <button class="music-btn" id="time-test-btn" title="測試時間">測試時間</button>
                        <button class="music-btn" id="reset-time-btn" title="重置時間" style="display: none;">重置時間</button>
                    </div>
                </div>
            </div>

            <!-- 底部主題切換控制項（移至最下方） -->
            <div class="footer-controls">
                <div class="theme-controls compact">
                    <span class="control-icon" title="主題">🎀</span>
                    <div class="theme-buttons">
                        <button class="music-btn" id="theme-toggle-btn" title="切換主題">切換為科技感</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 編輯課程表模態對話框 -->
    <div id="edit-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>編輯課程表</h2>
                <button class="close-button" id="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="schedule-controls">
                    <button id="add-class-btn" class="control-button">新增課程</button>
                    <button id="save-schedule-btn" class="control-button primary">儲存課表</button>
                </div>
                <div id="schedule-list" class="schedule-list">
                    <!-- 課程列表將在這裡動態生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 課程編輯表單模態對話框 -->
    <div id="class-form-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="form-title">新增課程</h2>
                <button class="close-button" id="close-form-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="class-form">
                    <div class="form-group">
                        <label for="class-day">星期：</label>
                        <select id="class-day" required>
                            <option value="Monday">星期一</option>
                            <option value="Tuesday">星期二</option>
                            <option value="Wednesday">星期三</option>
                            <option value="Thursday">星期四</option>
                            <option value="Friday">星期五</option>
                            <option value="Saturday">星期六</option>
                            <option value="Sunday">星期日</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="class-start">開始時間：</label>
                        <input type="time" id="class-start" required>
                    </div>
                    <div class="form-group">
                        <label for="class-end">結束時間：</label>
                        <input type="time" id="class-end" required>
                    </div>
                    <div class="form-group">
                        <label for="class-subject">科目：</label>
                        <input type="text" id="class-subject" placeholder="例如：國語" required>
                    </div>
                    <div class="form-group">
                        <label for="class-tasks">任務清單（每行一個）：</label>
                        <textarea id="class-tasks" rows="4" placeholder="例如：&#10;拿出課本&#10;準備筆記本&#10;複習上節課內容"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="cancel-form" class="button secondary">取消</button>
                        <button type="submit" class="button primary">儲存課程</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 倒數時間設置模態對話框 -->
    <div id="countdown-settings-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>設置倒數時間</h2>
                <button class="close-button" id="close-countdown-settings">&times;</button>
            </div>
            <div class="modal-body">
                <form id="countdown-settings-form">
                    <div class="form-group">
                        <label for="countdown-minutes">倒數時間（分鐘）：</label>
                        <input type="number" id="countdown-minutes" min="1" max="60" value="2" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="cancel-countdown-settings" class="button secondary">取消</button>
                        <button type="submit" class="button primary">儲存設置</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 休息時間編輯模態對話框 -->
    <div id="break-time-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="break-time-title">編輯休息時間</h2>
                <button class="close-button" id="close-break-time-modal">&times;</button>
            </div>
            <div class="modal-body">
                <form id="break-time-form">
                    <div class="form-group">
                        <label for="break-start-time">開始時間：</label>
                        <input type="time" id="break-start-time" required>
                    </div>
                    <div class="form-group">
                        <label for="break-end-time">結束時間：</label>
                        <input type="time" id="break-end-time" required>
                    </div>
                    <div class="form-group">
                        <label for="break-description">描述：</label>
                        <textarea id="break-description" rows="3" placeholder="例如：享用美味午餐，補充體力"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" id="cancel-break-time" class="button secondary">取消</button>
                        <button type="submit" class="button primary">儲存設定</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 測試時間模態對話框 -->
    <div id="time-test-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🕐 測試時間設定</h2>
                <button class="close-button" id="close-time-test">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="test-time-input">設定測試時間：</label>
                    <input type="time" id="test-time-input" required>
                    <small style="color: #6c757d; display: block; margin-top: 5px;">
                        設定一個時間來測試系統是否正確顯示課程狀態
                    </small>
                </div>
                <div class="form-group">
                    <label for="test-date-input">設定測試日期（可選）：</label>
                    <input type="date" id="test-date-input">
                    <small style="color: #6c757d; display: block; margin-top: 5px;">
                        不設定則使用今天的日期
                    </small>
                </div>
                <div class="form-actions">
                    <button type="button" id="cancel-time-test" class="button secondary">取消</button>
                    <button type="button" id="apply-test-time" class="button primary">套用測試時間</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 匯出課表模態對話框 -->
    <div id="export-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📤 匯出課表</h2>
                <button class="close-button" id="close-export-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="export-options">
                    <h3>選擇匯出格式：</h3>
                    <div class="export-format-grid">
                        <button class="export-format-btn" data-format="json">
                            <div class="format-icon">📄</div>
                            <div class="format-name">JSON</div>
                            <div class="format-desc">原始資料格式</div>
                        </button>
                        <button class="export-format-btn" data-format="csv">
                            <div class="format-icon">📊</div>
                            <div class="format-name">CSV</div>
                            <div class="format-desc">試算表格式</div>
                        </button>
                        <button class="export-format-btn" data-format="pdf">
                            <div class="format-icon">📋</div>
                            <div class="format-name">PDF</div>
                            <div class="format-desc">可列印文件</div>
                        </button>
                        <button class="export-format-btn" data-format="excel">
                            <div class="format-icon">📈</div>
                            <div class="format-name">Excel</div>
                            <div class="format-desc">Excel 工作表</div>
                        </button>
                    </div>
                    
                    <!-- 匯入功能區域 -->
                    <div class="import-section">
                        <h3>📂 匯入課表：</h3>
                        <div class="import-controls">
                            <div class="file-input-wrapper">
                                <input type="file" id="import-file-input" accept=".json" style="display: none;">
                                <button class="import-btn" id="import-file-btn">
                                    <div class="format-icon">📁</div>
                                    <div class="format-name">選擇檔案</div>
                                    <div class="format-desc">匯入 JSON 課表檔案</div>
                                </button>
                            </div>
                            <div class="import-info">
                                <p><strong>注意：</strong></p>
                                <ul>
                                    <li>僅支援 JSON 格式的課表檔案</li>
                                    <li>匯入後會替換當前的課表資料</li>
                                    <li>建議先備份現有課表</li>
                                    <li>課表會自動儲存到瀏覽器</li>
                                </ul>
                                <button class="reset-schedule-btn" id="reset-schedule-btn">
                                    🔄 恢復原始課表
                                </button>
                                <button class="export-js-btn" id="export-js-btn">
                                    📜 匯出為 JS 檔案
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="export-settings">
                        <h3>匯出設定：</h3>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="include-weekend" checked>
                                包含週末課程
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="include-tasks" checked>
                                包含任務清單
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="include-special-times" checked>
                                包含特殊時段（午餐、午休等）
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="export-filename">檔案名稱：</label>
                            <input type="text" id="export-filename" value="課程表" placeholder="輸入檔案名稱">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 音樂設定模態對話框 -->
    <div id="music-settings-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>🎵 音樂設定</h2>
                <button class="close-button" id="close-music-settings">&times;</button>
            </div>
            <div class="modal-body">
                <form id="music-settings-form">
                    <div class="form-group">
                        <label for="enable-music">啟用音樂提醒：</label>
                        <input type="checkbox" id="enable-music" checked>
                    </div>
                    
                    <div class="form-group">
                        <label for="music-volume">音量大小：</label>
                        <input type="range" id="music-volume" min="0" max="100" value="50">
                        <span id="volume-display">50%</span>
                    </div>
                    
                    <div class="form-group">
                        <label for="class-start-sound">上課鈴聲：</label>
                        <select id="class-start-sound">
                            <option value="none" selected>不播放</option>
                            <option value="bell">經典鈴聲</option>
                            <option value="chime">輕柔鐘聲</option>
                            <option value="beep">電子提示音</option>
                            <option value="ding">清脆鈴聲</option>
                            <option value="gong">鑼聲</option>
                            <option value="school-bell">學校鈴聲</option>
                            <option value="soft-chime">溫和鐘聲</option>
                            <option value="custom">自訂音樂</option>
                        </select>
                        <input type="file" id="class-start-file" accept="audio/*" style="display: none;">
                    </div>
                    
                    <div class="form-group">
                        <label for="countdown-end-sound">倒數結束音效：</label>
                        <select id="countdown-end-sound">
                            <option value="none" selected>不播放</option>
                            <option value="alarm">警報聲</option>
                            <option value="ding">叮咚聲</option>
                            <option value="whistle">哨聲</option>
                            <option value="beep-beep">連續嗶聲</option>
                            <option value="notification">通知音</option>
                            <option value="chime-end">結束鐘聲</option>
                            <option value="gentle-alert">溫和提醒</option>
                            <option value="custom">自訂音樂</option>
                        </select>
                        <input type="file" id="countdown-end-file" accept="audio/*" style="display: none;">
                    </div>

                    <div class="form-group">
                        <label for="lunch-prep-music">🍽️ 午餐工作音樂：</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <select id="lunch-prep-music" style="flex: 1;">
                                <option value="none">不播放</option>
                                <option value="energetic">活力音樂</option>
                                <option value="upbeat">節奏感音樂</option>
                                <option value="motivational">激勵音樂</option>
                                <option value="cheerful">輕快音樂</option>
                                <option value="custom">自訂音樂</option>
                            </select>
                            <button type="button" id="test-lunch-prep-music-btn" class="test-music-btn">🔊 測試</button>
                        </div>
                        <input type="file" id="lunch-prep-music-file" accept="audio/*" style="display: none;">
                    </div>

                    <div class="form-group">
                        <label for="lunch-music">🍽️ 中午用餐音樂：</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <select id="lunch-music" style="flex: 1;">
                                <option value="none">不播放</option>
                                <option value="relaxing">輕鬆音樂</option>
                                <option value="classical">古典音樂</option>
                                <option value="nature">自然音效</option>
                                <option value="custom">自訂音樂</option>
                            </select>
                            <button type="button" id="test-lunch-music-btn" class="test-music-btn">🔊 測試</button>
                        </div>
                        <input type="file" id="lunch-music-file" accept="audio/*" style="display: none;">
                    </div>

                    <div class="form-group">
                        <label for="nap-music">😴 午休音樂：</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <select id="nap-music" style="flex: 1;">
                                <option value="none">不播放</option>
                                <option value="lullaby">搖籃曲</option>
                                <option value="ambient">環境音樂</option>
                                <option value="white-noise">白噪音</option>
                                <option value="custom">自訂音樂</option>
                            </select>
                            <button type="button" id="test-nap-music-btn" class="test-music-btn">🔊 測試</button>
                        </div>
                        <input type="file" id="nap-music-file" accept="audio/*" style="display: none;">
                    </div>

                    <div class="form-group">
                        <label for="dismissal-music">🎒 放學準備音樂：</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <select id="dismissal-music" style="flex: 1;">
                                <option value="none">不播放</option>
                                <option value="cheerful">輕快音樂</option>
                                <option value="motivational">激勵音樂</option>
                                <option value="peaceful">平和音樂</option>
                                <option value="custom">自訂音樂</option>
                            </select>
                            <button type="button" id="test-dismissal-music-btn" class="test-music-btn">🔊 測試</button>
                        </div>
                        <input type="file" id="dismissal-music-file" accept="audio/*" style="display: none;">
                    </div>

                    <div class="form-group">
                        <label for="cleaning-music">🧹 掃地時間音樂：</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <select id="cleaning-music" style="flex: 1;">
                                <option value="none">不播放</option>
                                <option value="energetic">活力音樂</option>
                                <option value="upbeat">節奏感音樂</option>
                                <option value="motivational">激勵音樂</option>
                                <option value="cheerful">輕快音樂</option>
                                <option value="custom">自訂音樂</option>
                            </select>
                            <button type="button" id="test-cleaning-music-btn" class="test-music-btn">🔊 測試</button>
                        </div>
                        <input type="file" id="cleaning-music-file" accept="audio/*" style="display: none;">
                    </div>

                    <div class="form-group">
                        <label for="lunch-prep-time-start">午餐工作時間開始：</label>
                        <input type="time" id="lunch-prep-time-start" value="12:20">
                    </div>

                    <div class="form-group">
                        <label for="lunch-prep-time-end">午餐工作時間結束：</label>
                        <input type="time" id="lunch-prep-time-end" value="12:30">
                    </div>

                    <div class="form-group">
                        <label for="lunch-time-start">午餐時間開始：</label>
                        <input type="time" id="lunch-time-start" value="11:50">
                    </div>

                    <div class="form-group">
                        <label for="lunch-time-end">午餐時間結束：</label>
                        <input type="time" id="lunch-time-end" value="12:35">
                    </div>

                    <div class="form-group">
                        <label for="nap-time-start">午休時間開始：</label>
                        <input type="time" id="nap-time-start" value="12:35">
                    </div>

                    <div class="form-group">
                        <label for="nap-time-end">午休時間結束：</label>
                        <input type="time" id="nap-time-end" value="13:15">
                    </div>

                    <div class="form-group">
                        <label for="dismissal-time-start">放學準備時間開始：</label>
                        <input type="time" id="dismissal-time-start" value="15:45">
                    </div>

                    <div class="form-group">
                        <label for="dismissal-time-end">放學準備時間結束：</label>
                        <input type="time" id="dismissal-time-end" value="16:00">
                    </div>

                    <div class="form-group">
                        <label for="cleaning-time-start">掃地時間開始：</label>
                        <input type="time" id="cleaning-time-start" value="07:50">
                    </div>

                    <div class="form-group">
                        <label for="cleaning-time-end">掃地時間結束：</label>
                        <input type="time" id="cleaning-time-end" value="08:05">
                    </div>

                    <div class="form-group">
                        <label for="music-duration">音樂播放時長（秒）：</label>
                        <input type="number" id="music-duration" min="1" max="300" value="3">
                        <small style="color: #6c757d;">可設定 1-300 秒（5分鐘）</small>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" id="stop-test-music" class="button secondary" style="background: #dc3545;">🛑 停止測試音樂</button>
                        <button type="button" id="cancel-music-settings" class="button secondary">取消</button>
                        <button type="submit" class="button primary">儲存設定</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 載入課程表資料（解決 CORS 問題） -->
    <script src="schedule-data.js"></script>
    <script src="script.js"></script>
</body>
</html>
